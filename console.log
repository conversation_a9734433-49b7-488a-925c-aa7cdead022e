react refresh:37 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
Sidebar.js:37 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Sidebar.js:37.
    at Sidebar (http://localhost:3002/static/js/bundle.js:202987:81)
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Sidebar @ Sidebar.js:37
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20103
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Sidebar.js:37 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Sidebar.js:37.
    at Sidebar (http://localhost:3002/static/js/bundle.js:202987:81)
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Sidebar @ Sidebar.js:37
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20174
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:28478 Uncaught Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check the render method of `Sidebar`.
    at createFiberFromTypeAndProps (react-dom.development.js:28478:1)
    at createFiberFromElement (react-dom.development.js:28504:1)
    at createChild (react-dom.development.js:13345:1)
    at reconcileChildrenArray (react-dom.development.js:13640:1)
    at reconcileChildFibers (react-dom.development.js:14057:1)
    at reconcileChildren (react-dom.development.js:19186:1)
    at updateFragment (react-dom.development.js:19543:1)
    at beginWork (react-dom.development.js:21679:1)
    at HTMLUnknownElement.callCallback (react-dom.development.js:4164:1)
    at Object.invokeGuardedCallbackDev (react-dom.development.js:4213:1)
createFiberFromTypeAndProps @ react-dom.development.js:28478
createFiberFromElement @ react-dom.development.js:28504
createChild @ react-dom.development.js:13345
reconcileChildrenArray @ react-dom.development.js:13640
reconcileChildFibers @ react-dom.development.js:14057
reconcileChildren @ react-dom.development.js:19186
updateFragment @ react-dom.development.js:19543
beginWork @ react-dom.development.js:21679
callCallback @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
beginWork$1 @ react-dom.development.js:27490
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Dashboard.js:156 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Dashboard.js:156.
    at Dashboard (http://localhost:3002/static/js/bundle.js:203734:76)
    at RenderedRoute (http://localhost:3002/static/js/bundle.js:192663:5)
    at Routes (http://localhost:3002/static/js/bundle.js:193397:5)
    at main
    at http://localhost:3002/static/js/bundle.js:99867:18
    at Content
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Dashboard @ Dashboard.js:156
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20103
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Dashboard.js:156 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Dashboard.js:156.
    at Dashboard (http://localhost:3002/static/js/bundle.js:203734:76)
    at RenderedRoute (http://localhost:3002/static/js/bundle.js:192663:5)
    at Routes (http://localhost:3002/static/js/bundle.js:193397:5)
    at main
    at http://localhost:3002/static/js/bundle.js:99867:18
    at Content
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Dashboard @ Dashboard.js:156
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20174
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Sidebar.js:37 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Sidebar.js:37.
    at Sidebar (http://localhost:3002/static/js/bundle.js:202987:81)
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Sidebar @ Sidebar.js:37
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20103
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Sidebar.js:37 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Sidebar.js:37.
    at Sidebar (http://localhost:3002/static/js/bundle.js:202987:81)
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Sidebar @ Sidebar.js:37
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20174
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:28478 Uncaught Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check the render method of `Sidebar`.
    at createFiberFromTypeAndProps (react-dom.development.js:28478:1)
    at createFiberFromElement (react-dom.development.js:28504:1)
    at createChild (react-dom.development.js:13345:1)
    at reconcileChildrenArray (react-dom.development.js:13640:1)
    at reconcileChildFibers (react-dom.development.js:14057:1)
    at reconcileChildren (react-dom.development.js:19186:1)
    at updateFragment (react-dom.development.js:19543:1)
    at beginWork (react-dom.development.js:21679:1)
    at HTMLUnknownElement.callCallback (react-dom.development.js:4164:1)
    at Object.invokeGuardedCallbackDev (react-dom.development.js:4213:1)
createFiberFromTypeAndProps @ react-dom.development.js:28478
createFiberFromElement @ react-dom.development.js:28504
createChild @ react-dom.development.js:13345
reconcileChildrenArray @ react-dom.development.js:13640
reconcileChildFibers @ react-dom.development.js:14057
reconcileChildren @ react-dom.development.js:19186
updateFragment @ react-dom.development.js:19543
beginWork @ react-dom.development.js:21679
callCallback @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
beginWork$1 @ react-dom.development.js:27490
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Dashboard.js:156 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Dashboard.js:156.
    at Dashboard (http://localhost:3002/static/js/bundle.js:203734:76)
    at RenderedRoute (http://localhost:3002/static/js/bundle.js:192663:5)
    at Routes (http://localhost:3002/static/js/bundle.js:193397:5)
    at main
    at http://localhost:3002/static/js/bundle.js:99867:18
    at Content
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Dashboard @ Dashboard.js:156
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20103
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Dashboard.js:156 Warning: React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check your code at Dashboard.js:156.
    at Dashboard (http://localhost:3002/static/js/bundle.js:203734:76)
    at RenderedRoute (http://localhost:3002/static/js/bundle.js:192663:5)
    at Routes (http://localhost:3002/static/js/bundle.js:193397:5)
    at main
    at http://localhost:3002/static/js/bundle.js:99867:18
    at Content
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)
printWarning @ react-jsx-dev-runtime.development.js:87
error @ react-jsx-dev-runtime.development.js:61
jsxWithValidation @ react-jsx-dev-runtime.development.js:1245
Dashboard @ Dashboard.js:156
renderWithHooks @ react-dom.development.js:15486
mountIndeterminateComponent @ react-dom.development.js:20174
beginWork @ react-dom.development.js:21626
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:18704 The above error occurred in the <Fragment> component:

    at li
    at InternalItem (http://localhost:3002/static/js/bundle.js:143112:25)
    at InternalRawItem (http://localhost:3002/static/js/bundle.js:143539:52)
    at LegacyMenuItem (http://localhost:3002/static/js/bundle.js:139199:90)
    at http://localhost:3002/static/js/bundle.js:139230:21
    at MenuItem (http://localhost:3002/static/js/bundle.js:139354:24)
    at http://localhost:3002/static/js/bundle.js:77208:24
    at DomWrapper (http://localhost:3002/static/js/bundle.js:144599:90)
    at SingleObserver (http://localhost:3002/static/js/bundle.js:144643:24)
    at ResizeObserver (http://localhost:3002/static/js/bundle.js:144781:24)
    at http://localhost:3002/static/js/bundle.js:78036:34
    at Tooltip (http://localhost:3002/static/js/bundle.js:155814:32)
    at http://localhost:3002/static/js/bundle.js:120538:18
    at MenuItem (http://localhost:3002/static/js/bundle.js:100608:5)
    at ul
    at Overflow (http://localhost:3002/static/js/bundle.js:143229:32)
    at InheritableContextProvider (http://localhost:3002/static/js/bundle.js:140129:23)
    at http://localhost:3002/static/js/bundle.js:138683:27
    at http://localhost:3002/static/js/bundle.js:100941:55
    at http://localhost:3002/static/js/bundle.js:100849:64
    at div
    at aside
    at http://localhost:3002/static/js/bundle.js:99598:18
    at Sidebar (http://localhost:3002/static/js/bundle.js:202987:81)
    at div
    at http://localhost:3002/static/js/bundle.js:99887:42
    at Layout
    at App
    at MotionWrapper (http://localhost:3002/static/js/bundle.js:89816:59)
    at ProviderChildren (http://localhost:3002/static/js/bundle.js:90504:5)
    at ConfigProvider (http://localhost:3002/static/js/bundle.js:90804:54)
    at Router (http://localhost:3002/static/js/bundle.js:193331:15)
    at BrowserRouter (http://localhost:3002/static/js/bundle.js:191232:5)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
logCapturedError @ react-dom.development.js:18704
update.callback @ react-dom.development.js:18737
callCallback @ react-dom.development.js:15036
commitUpdateQueue @ react-dom.development.js:15057
commitLayoutEffectOnFiber @ react-dom.development.js:23430
commitLayoutMountEffects_complete @ react-dom.development.js:24727
commitLayoutEffects_begin @ react-dom.development.js:24713
commitLayoutEffects @ react-dom.development.js:24651
commitRootImpl @ react-dom.development.js:26862
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:25931
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:28478 Uncaught Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.

Check the render method of `Sidebar`.
    at createFiberFromTypeAndProps (react-dom.development.js:28478:1)
    at createFiberFromElement (react-dom.development.js:28504:1)
    at createChild (react-dom.development.js:13345:1)
    at reconcileChildrenArray (react-dom.development.js:13640:1)
    at reconcileChildFibers (react-dom.development.js:14057:1)
    at reconcileChildren (react-dom.development.js:19186:1)
    at updateFragment (react-dom.development.js:19543:1)
    at beginWork (react-dom.development.js:21679:1)
    at beginWork$1 (react-dom.development.js:27465:1)
    at performUnitOfWork (react-dom.development.js:26596:1)
createFiberFromTypeAndProps @ react-dom.development.js:28478
createFiberFromElement @ react-dom.development.js:28504
createChild @ react-dom.development.js:13345
reconcileChildrenArray @ react-dom.development.js:13640
reconcileChildFibers @ react-dom.development.js:14057
reconcileChildren @ react-dom.development.js:19186
updateFragment @ react-dom.development.js:19543
beginWork @ react-dom.development.js:21679
beginWork$1 @ react-dom.development.js:27465
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
recoverFromConcurrentError @ react-dom.development.js:25889
performConcurrentWorkOnRoot @ react-dom.development.js:25789
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
contentLogger.js-B8_glSTp.js:4 contentLogger
contentLogger.js-B8_glSTp.js:4 contentLogger
favicon.ico:1 
            
            
           GET http://localhost:3002/favicon.ico 500 (Internal Server Error)
