# 🌐 VPN Service Platform

## Deskripsi
Platform penjualan layanan VPN berbasis Mikrotik CHR. Pemesanan dilakukan melalui bot Telegram, verifikasi pembayaran manual oleh admin, dan provisioning akun VPN dilakukan secara otomatis. Admin dapat menambahkan beberapa CHR dan masing-masing fungsi sistem berjalan dalam container terpisah.

## Tujuan
- Menjual layanan VPN (internet, game, remote)
- Manajemen multi-CHR
- Otomatisasi provisioning akun VPN
- Pem<PERSON>han fungsi sistem (admin panel, Telegram bot, API backend)
- Pembayaran manual via QRIS dan konfirmasi manual

## Layanan VPN
| Tipe         | Fitur                                                                 |
|--------------|-----------------------------------------------------------------------|
| VPN Internet | Akses umum, akun PPP, limit bandwidth                                 |
| VPN Game     | Routing khusus game, akun PPP, limit bandwidth                        |
| VPN Remote   | Port forwarding (Winbox, API, Web) otomatis, limit bandwidth          |
| Mikhmon      | Setup manual oleh admin                                               |

## Komponen Sistem
- **telegram-poller**: Bot Telegram polling untuk pemesanan dan notifikasi
- **vpn-api**: Backend API utama untuk manajemen akun VPN, komunikasi ke CHR, dan provisioning
- **frontend-admin**: Panel admin berbasis web untuk mengelola user, konfirmasi pembayaran, dan manajemen CHR
- **mikrotik-api**: API perantara yang menangani eksekusi perintah ke CHR (login, tambah akun, port forwarding)

## Fitur Admin Panel
- Tambah / hapus CHR (input host, port, user, password)
- Atur limit bandwidth per jenis layanan (internet, game, remote)
- Konfirmasi manual pembayaran dan aktivasi langganan
- Monitoring log provisioning
- Auto provisioning akun VPN dan port forwarding

## Provisioning Otomatis
1. User order via bot Telegram
2. Admin konfirmasi pembayaran
3. Sistem provisioning:
   - Buat akun VPN (ppp) dengan limit sesuai plan
   - Untuk VPN Remote: alokasi 3 port acak yang belum digunakan untuk forwarding Winbox (8291), API (8728), dan Web (80)
   - Setup NAT Mikrotik secara otomatis
4. Informasi akun dikirim ke user oleh bot

## Struktur
```

vpn-service/
├── telegram-poller/
├── vpn-api/
├── frontend-admin/
├── mikrotik-api/
├── docker-compose.yml
└── README.md

```

## Credentials
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_ADMIN_CHAT_ID=222502004

## Docker compose command
docker compose up -d