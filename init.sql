-- VPN Service Database Schema

-- CHR Devices table
CREATE TABLE chr_devices (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    host VA<PERSON>HA<PERSON>(255) NOT NULL,
    port INTEGER DEFAULT 8728,
    username VA<PERSON>HA<PERSON>(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- VPN Plans table
CREATE TABLE vpn_plans (
    id SERIAL PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'internet', 'game', 'remote'
    price DECIMAL(10,2) NOT NULL,
    bandwidth_limit VARCHAR(50), -- e.g., '10M/10M'
    duration_days INTEGER DEFAULT 30,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    plan_id INTEGER REFERENCES vpn_plans(id),
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'paid', 'active', 'expired', 'cancelled'
    total_amount DECIMAL(10,2) NOT NULL,
    payment_proof TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- VPN Accounts table
CREATE TABLE vpn_accounts (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    chr_device_id INTEGER REFERENCES chr_devices(id),
    username VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    profile VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'disabled', 'expired'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- Port Forwarding table (for VPN Remote)
CREATE TABLE port_forwards (
    id SERIAL PRIMARY KEY,
    vpn_account_id INTEGER REFERENCES vpn_accounts(id),
    service_type VARCHAR(50) NOT NULL, -- 'winbox', 'api', 'web'
    external_port INTEGER NOT NULL,
    internal_port INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Used Ports table (to track allocated ports)
CREATE TABLE used_ports (
    id SERIAL PRIMARY KEY,
    chr_device_id INTEGER REFERENCES chr_devices(id),
    port INTEGER NOT NULL,
    is_used BOOLEAN DEFAULT true,
    allocated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(chr_device_id, port)
);

-- Logs table
CREATE TABLE logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL, -- 'info', 'warning', 'error'
    message TEXT NOT NULL,
    context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default VPN plans
INSERT INTO vpn_plans (name, type, price, bandwidth_limit, duration_days, description) VALUES
('VPN Internet Basic', 'internet', 15000, '10M/10M', 30, 'Akses internet umum dengan bandwidth 10Mbps'),
('VPN Internet Premium', 'internet', 25000, '20M/20M', 30, 'Akses internet umum dengan bandwidth 20Mbps'),
('VPN Game Basic', 'game', 20000, '15M/15M', 30, 'VPN khusus gaming dengan routing optimal'),
('VPN Game Premium', 'game', 35000, '30M/30M', 30, 'VPN gaming premium dengan bandwidth tinggi'),
('VPN Remote Basic', 'remote', 30000, '10M/10M', 30, 'VPN remote dengan port forwarding (Winbox, API, Web)'),
('VPN Remote Premium', 'remote', 50000, '20M/20M', 30, 'VPN remote premium dengan bandwidth tinggi');

-- Create indexes for better performance
CREATE INDEX idx_users_telegram_id ON users(telegram_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_vpn_accounts_order_id ON vpn_accounts(order_id);
CREATE INDEX idx_vpn_accounts_status ON vpn_accounts(status);
CREATE INDEX idx_used_ports_chr_device ON used_ports(chr_device_id);
CREATE INDEX idx_logs_created_at ON logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to chr_devices table
CREATE TRIGGER update_chr_devices_updated_at BEFORE UPDATE ON chr_devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
