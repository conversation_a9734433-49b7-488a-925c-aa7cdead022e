# Deployment Guide

Panduan deployment VPN Service Platform ke production.

## 🚀 Production Deployment

### Prerequisites
- Ubuntu 20.04+ atau CentOS 8+
- Docker & Docker Compose
- Domain name (optional)
- SSL certificate (recommended)
- Mikrotik CHR devices

### Server Requirements
- **Minimum**: 2 CPU, 4GB RAM, 20GB Storage
- **Recommended**: 4 CPU, 8GB RAM, 50GB Storage
- **Network**: Public IP, ports 80, 443, 3000-3003 open

## 🔧 Server Setup

### 1. Install Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Clone Repository
```bash
git clone <your-repository-url>
cd vpn-manager
```

### 3. Production Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit production settings
nano .env
```

### 4. Production Environment Variables
```bash
# Database (use strong passwords)
POSTGRES_DB=vpn_service_prod
POSTGRES_USER=vpn_admin
POSTGRES_PASSWORD=your_strong_password_here

# Telegram Bot (get from @BotFather)
TELEGRAM_BOT_TOKEN=your_production_bot_token
TELEGRAM_ADMIN_CHAT_ID=your_admin_chat_id

# API URLs (internal)
VPN_API_URL=http://vpn-api:3000
MIKROTIK_API_URL=http://mikrotik-api:3003

# Frontend (use your domain)
REACT_APP_API_URL=https://your-domain.com/api
```

## 🌐 Domain & SSL Setup

### 1. Domain Configuration
```bash
# Point your domain to server IP
# A record: your-domain.com -> YOUR_SERVER_IP
# A record: api.your-domain.com -> YOUR_SERVER_IP
```

### 2. SSL with Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d your-domain.com -d api.your-domain.com
```

### 3. Nginx Reverse Proxy
```bash
# Install Nginx
sudo apt install nginx

# Create configuration
sudo nano /etc/nginx/sites-available/vpn-service
```

```nginx
# Admin Panel
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# API
server {
    listen 80;
    server_name api.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name api.your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/vpn-service /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🐳 Production Docker Setup

### 1. Production Docker Compose
```bash
# Create production override
nano docker-compose.prod.yml
```

```yaml
version: '3.8'

services:
  postgres:
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}

  vpn-api:
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}

  mikrotik-api:
    restart: unless-stopped
    environment:
      - NODE_ENV=production

  telegram-poller:
    restart: unless-stopped
    environment:
      - NODE_ENV=production

  frontend-admin:
    restart: unless-stopped
    environment:
      - NODE_ENV=production

volumes:
  postgres_data:
    driver: local
```

### 2. Start Production Services
```bash
# Start with production config
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Verify all services running
docker-compose ps
```

## 🔒 Security Hardening

### 1. Firewall Configuration
```bash
# Install UFW
sudo apt install ufw

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow API ports (if needed externally)
sudo ufw allow 3000
sudo ufw allow 3003

# Enable firewall
sudo ufw enable
```

### 2. Database Security
```bash
# Change default passwords
# Restrict database access
# Enable SSL for database connections
```

### 3. Application Security
```bash
# Use environment variables for secrets
# Enable HTTPS only
# Implement rate limiting
# Regular security updates
```

## 📊 Monitoring Setup

### 1. Log Management
```bash
# Create log rotation
sudo nano /etc/logrotate.d/vpn-service
```

```
/var/log/vpn-service/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
```

### 2. Health Monitoring
```bash
# Create monitoring script
nano /opt/vpn-service/monitor.sh
```

```bash
#!/bin/bash
LOG_FILE="/var/log/vpn-service/health.log"

check_service() {
    local service=$1
    local url=$2
    
    if curl -f -s "$url" > /dev/null; then
        echo "$(date): $service - OK" >> $LOG_FILE
    else
        echo "$(date): $service - FAILED" >> $LOG_FILE
        # Send alert (email, Telegram, etc.)
    fi
}

check_service "VPN API" "http://localhost:3000/health"
check_service "Mikrotik API" "http://localhost:3003/health"
check_service "Admin Panel" "http://localhost:3002/"
```

```bash
# Make executable
chmod +x /opt/vpn-service/monitor.sh

# Add to crontab
crontab -e
# Add: */5 * * * * /opt/vpn-service/monitor.sh
```

## 💾 Backup Strategy

### 1. Database Backup
```bash
# Create backup script
nano /opt/vpn-service/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="vpn_service_prod"

# Create backup
docker-compose exec -T postgres pg_dump -U vpn_admin $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Keep only last 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: db_backup_$DATE.sql.gz"
```

```bash
# Schedule daily backups
crontab -e
# Add: 0 2 * * * /opt/vpn-service/backup.sh
```

### 2. Application Backup
```bash
# Backup uploads and configs
tar -czf /backups/app_backup_$(date +%Y%m%d).tar.gz \
    /opt/vpn-service/uploads \
    /opt/vpn-service/.env \
    /opt/vpn-service/docker-compose.yml
```

## 🔄 Updates & Maintenance

### 1. Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Verify services
docker-compose ps
```

### 2. Database Migrations
```bash
# If database schema changes
docker-compose exec vpn-api npm run migrate
```

### 3. System Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker
sudo apt update docker-ce docker-ce-cli containerd.io

# Restart services if needed
docker-compose restart
```

## 📈 Scaling

### 1. Horizontal Scaling
```bash
# Scale specific services
docker-compose up -d --scale vpn-api=2
docker-compose up -d --scale mikrotik-api=2
```

### 2. Load Balancer
```nginx
upstream vpn_api {
    server localhost:3000;
    server localhost:3001;
}

upstream mikrotik_api {
    server localhost:3003;
    server localhost:3004;
}
```

### 3. Database Scaling
- Read replicas for reporting
- Connection pooling
- Query optimization

## 🚨 Troubleshooting

### Common Issues

1. **Services won't start**
   ```bash
   docker-compose logs
   docker system prune
   docker-compose up -d --force-recreate
   ```

2. **Database connection issues**
   ```bash
   docker-compose exec postgres psql -U vpn_admin -d vpn_service_prod
   ```

3. **SSL certificate renewal**
   ```bash
   sudo certbot renew
   sudo systemctl reload nginx
   ```

### Emergency Procedures

1. **Service restart**
   ```bash
   docker-compose restart
   ```

2. **Database restore**
   ```bash
   gunzip -c /backups/db_backup_YYYYMMDD_HHMMSS.sql.gz | \
   docker-compose exec -T postgres psql -U vpn_admin -d vpn_service_prod
   ```

3. **Rollback deployment**
   ```bash
   git checkout previous-stable-commit
   docker-compose up -d --build
   ```

## ✅ Production Checklist

### Pre-deployment
- [ ] Server provisioned and secured
- [ ] Domain configured with SSL
- [ ] Environment variables set
- [ ] Database passwords changed
- [ ] Firewall configured
- [ ] Monitoring setup
- [ ] Backup strategy implemented

### Post-deployment
- [ ] All services running
- [ ] SSL certificates valid
- [ ] Database accessible
- [ ] Telegram bot responding
- [ ] Admin panel accessible
- [ ] CHR devices connected
- [ ] Monitoring alerts working
- [ ] Backups running

### Ongoing Maintenance
- [ ] Regular security updates
- [ ] Monitor service health
- [ ] Check backup integrity
- [ ] Review logs for errors
- [ ] Update SSL certificates
- [ ] Scale as needed

---

**Production deployment completed! 🚀**

Your VPN Service Platform is now ready for production use.
