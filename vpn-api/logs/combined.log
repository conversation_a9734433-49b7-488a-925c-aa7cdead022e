{"level":"error","message":"Failed to start server: connect ECONNREFUSED **********:5432","name":"SequelizeConnectionRefusedError","original":{"address":"**********","code":"ECONNREFUSED","errno":-111,"port":5432,"syscall":"connect"},"parent":{"address":"**********","code":"ECONNREFUSED","errno":-111,"port":5432,"syscall":"connect"},"service":"vpn-api","stack":"SequelizeConnectionRefusedError: connect ECONNREFUSED **********:5432\n    at Client._connectionCallback (/app/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/app/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/app/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:517:28)\n    at Socket.reportStreamError (/app/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-20T01:30:38.351Z"}
{"level":"info","message":"Database connected successfully","service":"vpn-api","timestamp":"2025-06-20T01:32:28.286Z"}
{"level":"info","message":"Database synchronized","service":"vpn-api","timestamp":"2025-06-20T01:32:28.622Z"}
{"level":"info","message":"Provisioning service started","service":"vpn-api","timestamp":"2025-06-20T01:32:28.623Z"}
{"level":"info","message":"VPN API service running on port 3000","service":"vpn-api","timestamp":"2025-06-20T01:32:28.642Z"}
