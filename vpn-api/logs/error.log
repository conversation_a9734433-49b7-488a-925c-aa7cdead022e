{"level":"error","message":"Failed to start server: connect ECONNREFUSED **********:5432","name":"SequelizeConnectionRefusedError","original":{"address":"**********","code":"ECONNREFUSED","errno":-111,"port":5432,"syscall":"connect"},"parent":{"address":"**********","code":"ECONNREFUSED","errno":-111,"port":5432,"syscall":"connect"},"service":"vpn-api","stack":"SequelizeConnectionRefusedError: connect ECONNREFUSED **********:5432\n    at Client._connectionCallback (/app/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/app/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/app/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:517:28)\n    at Socket.reportStreamError (/app/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-20T01:30:38.351Z"}
{"cause":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"code":"ENOTFOUND","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"host\":\"********\",\"port\":8085,\"username\":\"doko\",\"password\":\"rahasiasekali\"}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"76","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://mikrotik-api:3003/api/mikrotik/test-connection","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-3008,"hostname":"mikrotik-api","level":"error","message":"CHR connection test failed: getaddrinfo ENOTFOUND mikrotik-api","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":"76","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"http://mikrotik-api:3003/api/mikrotik/test-connection","_ended":false,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"76","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"hostname":"mikrotik-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":{"_closed":false,"_contentLength":"76","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":"[Circular]","strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/mikrotik/test-connection","pathname":"/api/mikrotik/test-connection","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[{"data":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"}}],"_requestBodyLength":76,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"service":"vpn-api","stack":"Error: getaddrinfo ENOTFOUND mikrotik-api\n    at AxiosError.from (/app/node_modules/axios/dist/node/axios.cjs:863:14)\n    at RedirectableRequest.handleRequestError (/app/node_modules/axios/dist/node/axios.cjs:3191:25)\n    at RedirectableRequest.emit (node:events:517:28)\n    at eventHandlers.<computed> (/app/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/chr.js:43:34","syscall":"getaddrinfo","timestamp":"2025-06-20T01:43:59.079Z"}
{"cause":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"code":"ENOTFOUND","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"host\":\"**********\",\"port\":8085,\"username\":\"doko\",\"password\":\"rahasiasekali\"}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"78","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://mikrotik-api:3003/api/mikrotik/test-connection","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-3008,"hostname":"mikrotik-api","level":"error","message":"CHR connection test failed: getaddrinfo ENOTFOUND mikrotik-api","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":"78","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 78\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 78\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,49,48,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":336,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":336,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 78\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,49,48,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":336,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":336,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"http://mikrotik-api:3003/api/mikrotik/test-connection","_ended":false,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"78","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"hostname":"mikrotik-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":{"_closed":false,"_contentLength":"78","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 78\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":"[Circular]","strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 78\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,49,48,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":336,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":336,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/mikrotik/test-connection","pathname":"/api/mikrotik/test-connection","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[{"data":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,49,48,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"}}],"_requestBodyLength":78,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"service":"vpn-api","stack":"Error: getaddrinfo ENOTFOUND mikrotik-api\n    at AxiosError.from (/app/node_modules/axios/dist/node/axios.cjs:863:14)\n    at RedirectableRequest.handleRequestError (/app/node_modules/axios/dist/node/axios.cjs:3191:25)\n    at RedirectableRequest.emit (node:events:517:28)\n    at eventHandlers.<computed> (/app/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/chr.js:43:34","syscall":"getaddrinfo","timestamp":"2025-06-20T01:44:12.297Z"}
{"cause":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"code":"ENOTFOUND","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"host\":\"********\",\"port\":8085,\"username\":\"doko\",\"password\":\"rahasiasekali\"}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"76","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://mikrotik-api:3003/api/mikrotik/test-connection","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-3008,"hostname":"mikrotik-api","level":"error","message":"CHR connection test failed: getaddrinfo ENOTFOUND mikrotik-api","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":"76","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"http://mikrotik-api:3003/api/mikrotik/test-connection","_ended":false,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"76","Content-Type":"application/json","User-Agent":"axios/1.10.0"},"hostname":"mikrotik-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"mikrotik-api:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"mikrotik-api","_httpMessage":{"_closed":false,"_contentLength":"76","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":false,"host":"mikrotik-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/mikrotik/test-connection","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":"[Circular]","strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_parent":null,"_pendingData":[{"chunk":"POST /api/mikrotik/test-connection HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 76\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: mikrotik-api:3003\r\nConnection: close\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":62552},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":true,"closed":true,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":true,"emitClose":false,"ended":false,"ending":false,"errorEmitted":true,"errored":{"code":"ENOTFOUND","errno":-3008,"hostname":"mikrotik-api","syscall":"getaddrinfo"},"finalCalled":false,"finished":false,"highWaterMark":16384,"length":334,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writelen":334,"writing":true},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/mikrotik/test-connection","pathname":"/api/mikrotik/test-connection","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[{"data":{"data":[123,34,104,111,115,116,34,58,34,49,48,46,48,46,49,46,49,34,44,34,112,111,114,116,34,58,56,48,56,53,44,34,117,115,101,114,110,97,109,101,34,58,34,100,111,107,111,34,44,34,112,97,115,115,119,111,114,100,34,58,34,114,97,104,97,115,105,97,115,101,107,97,108,105,34,125],"type":"Buffer"}}],"_requestBodyLength":76,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"service":"vpn-api","stack":"Error: getaddrinfo ENOTFOUND mikrotik-api\n    at AxiosError.from (/app/node_modules/axios/dist/node/axios.cjs:863:14)\n    at RedirectableRequest.handleRequestError (/app/node_modules/axios/dist/node/axios.cjs:3191:25)\n    at RedirectableRequest.emit (node:events:517:28)\n    at eventHandlers.<computed> (/app/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/chr.js:43:34","syscall":"getaddrinfo","timestamp":"2025-06-20T01:44:58.751Z"}
{"code":"ENOENT","errno":-2,"level":"error","message":"Unhandled error: ENOENT: no such file or directory, open 'uploads/payment-1750384669571-691020780.jpg'","path":"uploads/payment-1750384669571-691020780.jpg","service":"vpn-api","stack":"Error: ENOENT: no such file or directory, open 'uploads/payment-1750384669571-691020780.jpg'","storageErrors":[],"syscall":"open","timestamp":"2025-06-20T01:57:49.574Z"}
{"code":"ENOENT","errno":-2,"level":"error","message":"Unhandled error: ENOENT: no such file or directory, open 'uploads/payment-1750384756802-707569331.jpg'","path":"uploads/payment-1750384756802-707569331.jpg","service":"vpn-api","stack":"Error: ENOENT: no such file or directory, open 'uploads/payment-1750384756802-707569331.jpg'","storageErrors":[],"syscall":"open","timestamp":"2025-06-20T01:59:16.803Z"}
