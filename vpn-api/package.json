{"name": "vpn-api", "version": "1.0.0", "description": "VPN Service API Backend", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "pg": "^8.11.3", "sequelize": "^6.33.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "winston": "^3.10.0", "node-cron": "^3.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}