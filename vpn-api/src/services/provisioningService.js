const axios = require('axios');
const cron = require('node-cron');
const { Order, VpnPlan, ChrDevice, VpnAccount, PortForward, UsedPort } = require('../models');
const logger = require('../utils/logger');

const MIKROTIK_API_URL = process.env.MIKROTIK_API_URL || 'http://mikrotik-api:3003';

class ProvisioningService {
    constructor() {
        this.isRunning = false;
    }

    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        logger.info('Provisioning service started');

        // Check for expired accounts every hour
        cron.schedule('0 * * * *', () => {
            this.checkExpiredAccounts();
        });

        // Check for pending provisioning every 5 minutes
        cron.schedule('*/5 * * * *', () => {
            this.processPendingOrders();
        });
    }

    async provisionOrder(orderId) {
        try {
            const order = await Order.findByPk(orderId, {
                include: [{ model: VpnPlan }]
            });

            if (!order) {
                throw new Error('Order not found');
            }

            if (order.status !== 'active') {
                throw new Error('Order is not in active status');
            }

            // Check if already provisioned
            const existingAccount = await VpnAccount.findOne({ where: { order_id: orderId } });
            if (existingAccount) {
                logger.info(`Order ${orderId} already provisioned`);
                return existingAccount;
            }

            // Get available CHR device
            const chrDevice = await this.getAvailableChrDevice();
            if (!chrDevice) {
                throw new Error('No available CHR device');
            }

            // Generate account credentials
            const username = this.generateUsername();
            const password = this.generatePassword();
            const profileName = `${order.VpnPlan.type}_${order.VpnPlan.bandwidth_limit}`;

            // Create profile if not exists
            await this.createProfileIfNotExists(chrDevice, profileName, order.VpnPlan.bandwidth_limit);

            // Create PPP secret
            await this.createPPPSecret(chrDevice, username, password, profileName);

            // Create VPN account record
            const vpnAccount = await VpnAccount.create({
                order_id: orderId,
                chr_device_id: chrDevice.id,
                username,
                password,
                profile: profileName,
                status: 'active',
                expires_at: order.expires_at
            });

            // Setup port forwarding for remote VPN
            if (order.VpnPlan.type === 'remote') {
                await this.setupPortForwarding(vpnAccount, chrDevice);
            }

            logger.info(`Successfully provisioned order ${orderId} with account ${username}`);
            return vpnAccount;

        } catch (error) {
            logger.error(`Failed to provision order ${orderId}:`, error);
            throw error;
        }
    }

    async getAvailableChrDevice() {
        const devices = await ChrDevice.findAll({
            where: { is_active: true },
            order: [['id', 'ASC']]
        });

        // Simple round-robin selection
        // In production, you might want to implement load balancing
        return devices.length > 0 ? devices[0] : null;
    }

    generateUsername() {
        const prefix = 'vpn';
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.random().toString(36).substring(2, 5);
        return `${prefix}${timestamp}${random}`;
    }

    generatePassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = '';
        for (let i = 0; i < 12; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
    }

    async createProfileIfNotExists(chrDevice, profileName, rateLimit) {
        try {
            await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/create-profile`, {
                chrConfig: {
                    host: chrDevice.host,
                    port: chrDevice.port,
                    username: chrDevice.username,
                    password: chrDevice.password
                },
                profileName,
                rateLimit
            });
        } catch (error) {
            // Profile might already exist, which is fine
            if (!error.response?.data?.error?.includes('already exists')) {
                throw error;
            }
        }
    }

    async createPPPSecret(chrDevice, username, password, profile) {
        const response = await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/create-account`, {
            chrConfig: {
                host: chrDevice.host,
                port: chrDevice.port,
                username: chrDevice.username,
                password: chrDevice.password
            },
            username,
            password,
            profile
        });

        if (!response.data.success) {
            throw new Error('Failed to create PPP secret');
        }
    }

    async setupPortForwarding(vpnAccount, chrDevice) {
        try {
            // Get available ports
            const portsResponse = await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/available-ports`, {
                chrConfig: {
                    host: chrDevice.host,
                    port: chrDevice.port,
                    username: chrDevice.username,
                    password: chrDevice.password
                },
                startPort: 10000,
                endPort: 65535,
                count: 3
            });

            const availablePorts = portsResponse.data.ports;
            if (availablePorts.length < 3) {
                throw new Error('Not enough available ports for port forwarding');
            }

            const services = [
                { type: 'winbox', internal: 8291, external: availablePorts[0] },
                { type: 'api', internal: 8728, external: availablePorts[1] },
                { type: 'web', internal: 80, external: availablePorts[2] }
            ];

            // Assume VPN client will get IP ********** (first available in pool)
            const internalIP = '**********';

            for (const service of services) {
                // Add port forwarding rule
                await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/add-port-forward`, {
                    chrConfig: {
                        host: chrDevice.host,
                        port: chrDevice.port,
                        username: chrDevice.username,
                        password: chrDevice.password
                    },
                    externalPort: service.external,
                    internalIP,
                    internalPort: service.internal,
                    protocol: 'tcp'
                });

                // Create port forward record
                await PortForward.create({
                    vpn_account_id: vpnAccount.id,
                    service_type: service.type,
                    external_port: service.external,
                    internal_port: service.internal,
                    is_active: true
                });

                // Mark port as used
                await UsedPort.create({
                    chr_device_id: chrDevice.id,
                    port: service.external,
                    is_used: true
                });
            }

            logger.info(`Port forwarding setup completed for VPN account ${vpnAccount.username}`);

        } catch (error) {
            logger.error(`Failed to setup port forwarding for VPN account ${vpnAccount.username}:`, error);
            throw error;
        }
    }

    async checkExpiredAccounts() {
        try {
            const expiredAccounts = await VpnAccount.findAll({
                where: {
                    status: 'active',
                    expires_at: {
                        [require('sequelize').Op.lt]: new Date()
                    }
                },
                include: [{ model: ChrDevice }]
            });

            for (const account of expiredAccounts) {
                await this.deactivateAccount(account);
            }

            if (expiredAccounts.length > 0) {
                logger.info(`Deactivated ${expiredAccounts.length} expired accounts`);
            }

        } catch (error) {
            logger.error('Error checking expired accounts:', error);
        }
    }

    async deactivateAccount(vpnAccount) {
        try {
            const chrDevice = vpnAccount.ChrDevice;

            // Remove PPP secret
            await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/remove-account`, {
                chrConfig: {
                    host: chrDevice.host,
                    port: chrDevice.port,
                    username: chrDevice.username,
                    password: chrDevice.password
                },
                username: vpnAccount.username
            });

            // Remove port forwarding if exists
            const portForwards = await PortForward.findAll({
                where: { vpn_account_id: vpnAccount.id, is_active: true }
            });

            for (const portForward of portForwards) {
                await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/remove-port-forward`, {
                    chrConfig: {
                        host: chrDevice.host,
                        port: chrDevice.port,
                        username: chrDevice.username,
                        password: chrDevice.password
                    },
                    externalPort: portForward.external_port
                });

                await portForward.update({ is_active: false });

                // Free up the port
                await UsedPort.update(
                    { is_used: false },
                    { where: { chr_device_id: chrDevice.id, port: portForward.external_port } }
                );
            }

            // Update account status
            await vpnAccount.update({ status: 'expired' });

            logger.info(`Deactivated expired VPN account ${vpnAccount.username}`);

        } catch (error) {
            logger.error(`Failed to deactivate VPN account ${vpnAccount.username}:`, error);
        }
    }

    async processPendingOrders() {
        try {
            const pendingOrders = await Order.findAll({
                where: { status: 'active' },
                include: [
                    { model: VpnPlan },
                    { model: VpnAccount, required: false }
                ]
            });

            const unprovisioned = pendingOrders.filter(order => !order.VpnAccount);

            for (const order of unprovisioned) {
                try {
                    await this.provisionOrder(order.id);
                } catch (error) {
                    logger.error(`Failed to provision order ${order.id}:`, error);
                }
            }

        } catch (error) {
            logger.error('Error processing pending orders:', error);
        }
    }
}

module.exports = new ProvisioningService();
