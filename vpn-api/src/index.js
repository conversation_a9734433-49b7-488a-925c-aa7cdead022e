const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { sequelize } = require('./models');
const logger = require('./utils/logger');

// Routes
const usersRoutes = require('./routes/users');
const ordersRoutes = require('./routes/orders');
const plansRoutes = require('./routes/plans');
const chrRoutes = require('./routes/chr');
const vpnAccountsRoutes = require('./routes/vpnAccounts');

// Services
const provisioningService = require('./services/provisioningService');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Routes
app.use('/api/users', usersRoutes);
app.use('/api/orders', ordersRoutes);
app.use('/api/plans', plansRoutes);
app.use('/api/chr', chrRoutes);
app.use('/api/vpn-accounts', vpnAccountsRoutes);

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'vpn-api' });
});

// Error handling middleware
app.use((err, req, res, next) => {
    logger.error('Unhandled error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

// Database connection and server start
async function startServer() {
    try {
        await sequelize.authenticate();
        logger.info('Database connected successfully');
        
        await sequelize.sync();
        logger.info('Database synchronized');

        // Start provisioning service
        provisioningService.start();
        
        app.listen(PORT, () => {
            logger.info(`VPN API service running on port ${PORT}`);
        });
    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();

module.exports = app;
