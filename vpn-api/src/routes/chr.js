const express = require('express');
const router = express.Router();
const { ChrDevice } = require('../models');
const logger = require('../utils/logger');
const axios = require('axios');

const MIKROTIK_API_URL = process.env.MIKROTIK_API_URL || 'http://mikrotik-api:3003';

// Get all CHR devices
router.get('/', async (req, res) => {
    try {
        const devices = await ChrDevice.findAll({
            order: [['created_at', 'DESC']]
        });
        res.json(devices);
    } catch (error) {
        logger.error('Get CHR devices error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get CHR device by ID
router.get('/:id', async (req, res) => {
    try {
        const device = await ChrDevice.findByPk(req.params.id);
        if (!device) {
            return res.status(404).json({ error: 'CHR device not found' });
        }
        res.json(device);
    } catch (error) {
        logger.error('Get CHR device error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create new CHR device
router.post('/', async (req, res) => {
    try {
        const { name, host, port, username, password } = req.body;
        
        // Test connection before saving
        try {
            const testResponse = await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/test-connection`, {
                host, port, username, password
            });
            
            if (!testResponse.data.success) {
                return res.status(400).json({ 
                    error: 'Failed to connect to CHR device',
                    details: testResponse.data.error 
                });
            }
        } catch (testError) {
            logger.error('CHR connection test failed:', testError);
            return res.status(400).json({ 
                error: 'Failed to test CHR connection',
                details: testError.message 
            });
        }

        const device = await ChrDevice.create({
            name, host, port: port || 8728, username, password
        });

        res.status(201).json(device);
    } catch (error) {
        logger.error('Create CHR device error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Update CHR device
router.put('/:id', async (req, res) => {
    try {
        const device = await ChrDevice.findByPk(req.params.id);
        if (!device) {
            return res.status(404).json({ error: 'CHR device not found' });
        }

        // Test connection if credentials changed
        const { host, port, username, password } = req.body;
        if (host || port || username || password) {
            const testConfig = {
                host: host || device.host,
                port: port || device.port,
                username: username || device.username,
                password: password || device.password
            };

            try {
                const testResponse = await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/test-connection`, testConfig);
                
                if (!testResponse.data.success) {
                    return res.status(400).json({ 
                        error: 'Failed to connect to CHR device with new credentials',
                        details: testResponse.data.error 
                    });
                }
            } catch (testError) {
                logger.error('CHR connection test failed:', testError);
                return res.status(400).json({ 
                    error: 'Failed to test CHR connection',
                    details: testError.message 
                });
            }
        }

        await device.update({
            ...req.body,
            updated_at: new Date()
        });
        
        res.json(device);
    } catch (error) {
        logger.error('Update CHR device error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Test CHR device connection
router.post('/:id/test', async (req, res) => {
    try {
        const device = await ChrDevice.findByPk(req.params.id);
        if (!device) {
            return res.status(404).json({ error: 'CHR device not found' });
        }

        const testResponse = await axios.post(`${MIKROTIK_API_URL}/api/mikrotik/test-connection`, {
            host: device.host,
            port: device.port,
            username: device.username,
            password: device.password
        });

        res.json(testResponse.data);
    } catch (error) {
        logger.error('Test CHR device error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Delete CHR device
router.delete('/:id', async (req, res) => {
    try {
        const device = await ChrDevice.findByPk(req.params.id);
        if (!device) {
            return res.status(404).json({ error: 'CHR device not found' });
        }

        await device.update({ is_active: false });
        res.json({ message: 'CHR device deactivated successfully' });
    } catch (error) {
        logger.error('Delete CHR device error:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
