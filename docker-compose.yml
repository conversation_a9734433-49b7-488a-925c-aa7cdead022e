version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: vpn_service
      POSTGRES_USER: vpn_user
      POSTGRES_PASSWORD: vpn_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vpn_user -d vpn_service"]
      interval: 5s
      timeout: 5s
      retries: 5

  vpn-api:
    build: ./vpn-api
    environment:
      - DATABASE_URL=************************************************/vpn_service
      - PORT=3000
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - vpn-network
    volumes:
      - ./vpn-api:/app
      - /app/node_modules

  mikrotik-api:
    build: ./mikrotik-api
    environment:
      - PORT=3003
      - VPN_API_URL=http://vpn-api:3000
    ports:
      - "3003:3003"
    networks:
      - vpn-network
    volumes:
      - ./mikrotik-api:/app
      - /app/node_modules

  telegram-poller:
    build: ./telegram-poller
    environment:
      - TELEGRAM_BOT_TOKEN=**********:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk
      - TELEGRAM_ADMIN_CHAT_ID=222502004
      - VPN_API_URL=http://vpn-api:3000
    depends_on:
      - vpn-api
    networks:
      - vpn-network
    volumes:
      - ./telegram-poller:/app
      - /app/node_modules
      - ./assets:/app/assets

  frontend-admin:
    build: ./frontend-admin
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - PORT=3002
    ports:
      - "3002:3002"
    depends_on:
      - vpn-api
    networks:
      - vpn-network
    volumes:
      - ./frontend-admin:/app
      - /app/node_modules

networks:
  vpn-network:
    driver: bridge

volumes:
  postgres_data:
