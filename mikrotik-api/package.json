{"name": "mikrotik-api", "version": "1.0.0", "description": "Mikrotik API service for VPN management", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "node-routeros": "^1.1.1", "axios": "^1.5.0", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}}