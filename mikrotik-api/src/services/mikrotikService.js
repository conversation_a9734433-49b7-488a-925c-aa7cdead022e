const { RouterOSAPI } = require('node-routeros');
const logger = require('../utils/logger');

class MikrotikService {
    constructor() {
        this.connections = new Map();
    }

    async connect(host, port, username, password) {
        const connectionKey = `${host}:${port}`;
        
        if (this.connections.has(connectionKey)) {
            return this.connections.get(connectionKey);
        }

        try {
            const api = new RouterOSAPI({
                host,
                user: username,
                password,
                port: port || 8728,
                timeout: 10
            });

            await api.connect();
            this.connections.set(connectionKey, api);
            logger.info(`Connected to Mikrotik ${host}:${port}`);
            return api;
        } catch (error) {
            logger.error(`Failed to connect to Mikrotik ${host}:${port}:`, error);
            throw error;
        }
    }

    async disconnect(host, port) {
        const connectionKey = `${host}:${port}`;
        const api = this.connections.get(connectionKey);
        
        if (api) {
            await api.close();
            this.connections.delete(connectionKey);
            logger.info(`Disconnected from Mikrotik ${host}:${port}`);
        }
    }

    async createPPPSecret(chrConfig, username, password, profile, service = 'ppp') {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            const result = await api.write('/ppp/secret/add', [
                `=name=${username}`,
                `=password=${password}`,
                `=profile=${profile}`,
                `=service=${service}`
            ]);

            logger.info(`Created PPP secret for user ${username} on ${chrConfig.host}`);
            return result;
        } catch (error) {
            logger.error(`Failed to create PPP secret for ${username}:`, error);
            throw error;
        }
    }

    async removePPPSecret(chrConfig, username) {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            // Find the secret first
            const secrets = await api.write('/ppp/secret/print', [`=name=${username}`]);
            
            if (secrets.length > 0) {
                await api.write('/ppp/secret/remove', [`=.id=${secrets[0]['.id']}`]);
                logger.info(`Removed PPP secret for user ${username} on ${chrConfig.host}`);
            }
        } catch (error) {
            logger.error(`Failed to remove PPP secret for ${username}:`, error);
            throw error;
        }
    }

    async createProfile(chrConfig, profileName, rateLimit) {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            const result = await api.write('/ppp/profile/add', [
                `=name=${profileName}`,
                `=rate-limit=${rateLimit}`,
                '=local-address=**********',
                '=remote-address=**********-************'
            ]);

            logger.info(`Created PPP profile ${profileName} on ${chrConfig.host}`);
            return result;
        } catch (error) {
            logger.error(`Failed to create PPP profile ${profileName}:`, error);
            throw error;
        }
    }

    async addPortForwarding(chrConfig, externalPort, internalIP, internalPort, protocol = 'tcp') {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            const result = await api.write('/ip/firewall/nat/add', [
                '=chain=dstnat',
                `=protocol=${protocol}`,
                `=dst-port=${externalPort}`,
                '=action=dst-nat',
                `=to-addresses=${internalIP}`,
                `=to-ports=${internalPort}`
            ]);

            logger.info(`Added port forwarding ${externalPort}->${internalIP}:${internalPort} on ${chrConfig.host}`);
            return result;
        } catch (error) {
            logger.error(`Failed to add port forwarding:`, error);
            throw error;
        }
    }

    async removePortForwarding(chrConfig, externalPort) {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            // Find the NAT rule
            const natRules = await api.write('/ip/firewall/nat/print', [
                '=chain=dstnat',
                `=dst-port=${externalPort}`
            ]);
            
            if (natRules.length > 0) {
                await api.write('/ip/firewall/nat/remove', [`=.id=${natRules[0]['.id']}`]);
                logger.info(`Removed port forwarding for port ${externalPort} on ${chrConfig.host}`);
            }
        } catch (error) {
            logger.error(`Failed to remove port forwarding:`, error);
            throw error;
        }
    }

    async getAvailablePorts(chrConfig, startPort = 10000, endPort = 65535, count = 3) {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            
            // Get existing NAT rules
            const natRules = await api.write('/ip/firewall/nat/print', ['=chain=dstnat']);
            const usedPorts = new Set();
            
            natRules.forEach(rule => {
                if (rule['dst-port']) {
                    usedPorts.add(parseInt(rule['dst-port']));
                }
            });

            const availablePorts = [];
            for (let port = startPort; port <= endPort && availablePorts.length < count; port++) {
                if (!usedPorts.has(port)) {
                    availablePorts.push(port);
                }
            }

            return availablePorts;
        } catch (error) {
            logger.error(`Failed to get available ports:`, error);
            throw error;
        }
    }

    async testConnection(chrConfig) {
        try {
            const api = await this.connect(chrConfig.host, chrConfig.port, chrConfig.username, chrConfig.password);
            const identity = await api.write('/system/identity/print');
            return { success: true, identity: identity[0].name };
        } catch (error) {
            logger.error(`Connection test failed for ${chrConfig.host}:`, error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = new MikrotikService();
