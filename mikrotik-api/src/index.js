const express = require('express');
const cors = require('cors');
require('dotenv').config();

const mikrotikRoutes = require('./routes/mikrotik');
const logger = require('./utils/logger');

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/mikrotik', mikrotikRoutes);

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'mikrotik-api' });
});

// Error handling middleware
app.use((err, req, res, next) => {
    logger.error('Unhandled error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

app.listen(PORT, () => {
    logger.info(`Mikrotik API service running on port ${PORT}`);
});

module.exports = app;
