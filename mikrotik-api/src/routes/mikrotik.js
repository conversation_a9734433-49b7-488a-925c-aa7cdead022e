const express = require('express');
const router = express.Router();
const mikrotikService = require('../services/mikrotikService');
const logger = require('../utils/logger');

// Test connection to CHR
router.post('/test-connection', async (req, res) => {
    try {
        const { host, port, username, password } = req.body;
        const result = await mikrotikService.testConnection({ host, port, username, password });
        res.json(result);
    } catch (error) {
        logger.error('Test connection error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create PPP secret (VPN account)
router.post('/create-account', async (req, res) => {
    try {
        const { chrConfig, username, password, profile } = req.body;
        const result = await mikrotikService.createPPPSecret(chrConfig, username, password, profile);
        res.json({ success: true, result });
    } catch (error) {
        logger.error('Create account error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Remove PPP secret
router.post('/remove-account', async (req, res) => {
    try {
        const { chrConfig, username } = req.body;
        await mikrotikService.removePPPSecret(chrConfig, username);
        res.json({ success: true });
    } catch (error) {
        logger.error('Remove account error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create PPP profile
router.post('/create-profile', async (req, res) => {
    try {
        const { chrConfig, profileName, rateLimit } = req.body;
        const result = await mikrotikService.createProfile(chrConfig, profileName, rateLimit);
        res.json({ success: true, result });
    } catch (error) {
        logger.error('Create profile error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Add port forwarding
router.post('/add-port-forward', async (req, res) => {
    try {
        const { chrConfig, externalPort, internalIP, internalPort, protocol } = req.body;
        const result = await mikrotikService.addPortForwarding(
            chrConfig, 
            externalPort, 
            internalIP, 
            internalPort, 
            protocol
        );
        res.json({ success: true, result });
    } catch (error) {
        logger.error('Add port forwarding error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Remove port forwarding
router.post('/remove-port-forward', async (req, res) => {
    try {
        const { chrConfig, externalPort } = req.body;
        await mikrotikService.removePortForwarding(chrConfig, externalPort);
        res.json({ success: true });
    } catch (error) {
        logger.error('Remove port forwarding error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get available ports
router.post('/available-ports', async (req, res) => {
    try {
        const { chrConfig, startPort, endPort, count } = req.body;
        const ports = await mikrotikService.getAvailablePorts(chrConfig, startPort, endPort, count);
        res.json({ success: true, ports });
    } catch (error) {
        logger.error('Get available ports error:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
