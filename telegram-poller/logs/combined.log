{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:30:38.565Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:30:44 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:30:44.223Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:30:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:30:54.764Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:31:07 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:31:07.723Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:31:23 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:31:23.484Z"}
{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:32:27.918Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:32:37 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:32:37.628Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:32:48 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:32:48.962Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:00 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:00.437Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:17 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:17.767Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:37 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:37.466Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:54.834Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:11 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:11.996Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:29 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:29.281Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:46 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:46.562Z"}
{"code":"EFATAL","level":"error","message":"Polling error: EFATAL: Error: read ETIMEDOUT","service":"telegram-poller","stack":"RequestError: Error: read ETIMEDOUT\n    at new RequestError (/app/node_modules/request-promise-core/lib/errors.js:14:15)\n    at plumbing.callback (/app/node_modules/request-promise-core/lib/plumbing.js:87:29)\n    at Request.RP$callback [as _callback] (/app/node_modules/request-promise-core/lib/plumbing.js:46:31)\n    at self.callback (/app/node_modules/@cypress/request/request.js:183:22)\n    at Request.emit (node:events:517:28)\n    at Request.onRequestError (/app/node_modules/@cypress/request/request.js:869:8)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-20T01:35:06.598Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:21 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:21.308Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:38 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:38.431Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:55 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:55.491Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:36:12 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:36:12.716Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:36:55 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:36:55.318Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:12 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:12.763Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:30 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:30.220Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:47 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:47.588Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:04 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:04.839Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:21 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:21.923Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:38 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:38.932Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:56 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:56.163Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:13 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:13.441Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:30 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:30.609Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:47 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:47.804Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:04 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:04.958Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:22 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:22.209Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:39 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:39.411Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:56 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:56.705Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:13 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:13.914Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:31 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:31.518Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:48 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:48.747Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:05 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:06.014Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:23 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:23.346Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:40 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:40.928Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:58 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:58.275Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:15 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:15.428Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:32 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:32.675Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:49 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:49.954Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:07 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:07.147Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:24 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:24.674Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"close","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:42 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:42.155Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:59 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:59.906Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:45:17 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:45:17.333Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:00 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:00.776Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:18 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:18.246Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:35 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:35.601Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:53 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:53.169Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:10 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:10.747Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:28 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:28.167Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:45 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:45.512Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:48:02 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:48:02.750Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:48:20 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:48:20.150Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:48:37 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:48:37.449Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:48:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:48:54.814Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:49:12 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:49:12.344Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:49:29 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:49:29.491Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:49:46 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:49:47.078Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:50:04 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:50:04.534Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:50:21 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:50:22.053Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:50:39 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:50:46.928Z"}
{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:50:49.186Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:51:15 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:51:15.319Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:51:25 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:51:25.223Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:51:36 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:51:36.599Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:51:47 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:51:47.917Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:52:05 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:52:05.221Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:52:22 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:52:22.389Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:52:39 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:52:39.659Z"}
{"level":"warn","message":"QRIS image not found for order 2. Please add qris-payment.jpg or qris-payment.png to assets/images/","service":"telegram-poller","timestamp":"2025-06-20T01:52:55.406Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:53:05 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:53:05.778Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:53:23 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:53:23.210Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:53:40 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:53:40.584Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:53:57 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:53:57.957Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:54:15 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:54:15.396Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:54:33 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:54:33.243Z"}
{"level":"warn","message":"QRIS image not found for order 3. Please add qris-payment.jpg or qris-payment.png to assets/images/","service":"telegram-poller","timestamp":"2025-06-20T01:54:52.587Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:55:07 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:55:07.911Z"}
{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:55:25.190Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:55:42 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:55:42.620Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:55:59 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:56:00.045Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:56:17 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:56:17.405Z"}
{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:56:35.388Z"}
{"level":"info","message":"QRIS image sent for order 4","service":"telegram-poller","timestamp":"2025-06-20T01:56:49.379Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:56:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:56:55.066Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:57:05 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:57:06.019Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:57:26 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:57:27.042Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:57:44 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:57:44.262Z"}
{"code":"ERR_BAD_RESPONSE","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------911472405368540101322809","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Failed to upload payment proof: Request failed with status code 500","name":"AxiosError","request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------911472405368540101322809\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,50,2,1,1,2,2,3,4,4,2,19,2,4,32,44,174,199,221,135,44,226,32,169,64,61,215,78,195,104,92,16,8,183,8,80,21,71,90,109,246,27,235,228,100,61,12,4,48,77,167,211,66,47,46,202,54,212,44,187,182,16,194,20,201,19,214,13,132,93,200,172,88,241,43,11,7,123,143,68,126,205,137,197,100,109,136,180,209,127,138,159,134,75,251,54,154,161,6,2,4,104,84,192,28,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,159,43,52,34,221,127,72,201,190,135,23,134,17,254,108,5,251,33,220,0,99,84,90,120,232,60,38,225,253,145,234,31,246,206,127,11,9,92,149,140,230,145,40,138,201,242,252,150,11,174,237,182,220,210,158,74,177,112,176,245,217,210,43,38,16,157,127,97,147,124,51,79,64,111,83,101,98,49,84,60,126,88,64,255,216,102,130,155,210,6,112,87,133,94,117,208,36,42,217,106,168,244,147,189,78,131,179,18,153,187,125,99,105,158,56,249,181,204,134,67,17,59,226,69,137,63,143,110,241,217,98,110,240,206,10,107,170,215,39,162,5,208,171,139,75,18,243,78,93,252,119,19,78,125,2,201,47,74,131,231,139,154,94,174,145,72,150,70,3,83,71,222,186,184,39,126,82,139,44,137,91,238,221,9,194,26,248,234,132,218,41,186,5,138,224,155,107,21,48,186,252,5,78,164,47,21,104,32,22,123,81,120,138,50,131,27,239,155,159,183,150,173,170,58,195,165,89,61,14,213,39,133,57,82,61,66,15,165,124,147,174,6,2,4,18,0,62,100,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:57:49 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------911472405368540101322809","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":"Internal server error"},"headers":{"access-control-allow-origin":"*","connection":"close","content-length":"33","content-type":"application/json; charset=utf-8","date":"Fri, 20 Jun 2025 01:57:49 GMT","etag":"W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","x-powered-by":"Express"},"request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------911472405368540101322809\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,50,2,1,1,2,2,3,4,4,2,19,2,4,32,44,174,199,221,135,44,226,32,169,64,61,215,78,195,104,92,16,8,183,8,80,21,71,90,109,246,27,235,228,100,61,12,4,48,77,167,211,66,47,46,202,54,212,44,187,182,16,194,20,201,19,214,13,132,93,200,172,88,241,43,11,7,123,143,68,126,205,137,197,100,109,136,180,209,127,138,159,134,75,251,54,154,161,6,2,4,104,84,192,28,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,159,43,52,34,221,127,72,201,190,135,23,134,17,254,108,5,251,33,220,0,99,84,90,120,232,60,38,225,253,145,234,31,246,206,127,11,9,92,149,140,230,145,40,138,201,242,252,150,11,174,237,182,220,210,158,74,177,112,176,245,217,210,43,38,16,157,127,97,147,124,51,79,64,111,83,101,98,49,84,60,126,88,64,255,216,102,130,155,210,6,112,87,133,94,117,208,36,42,217,106,168,244,147,189,78,131,179,18,153,187,125,99,105,158,56,249,181,204,134,67,17,59,226,69,137,63,143,110,241,217,98,110,240,206,10,107,170,215,39,162,5,208,171,139,75,18,243,78,93,252,119,19,78,125,2,201,47,74,131,231,139,154,94,174,145,72,150,70,3,83,71,222,186,184,39,126,82,139,44,137,91,238,221,9,194,26,248,234,132,218,41,186,5,138,224,155,107,21,48,186,252,5,78,164,47,21,104,32,22,123,81,120,138,50,131,27,239,155,159,183,150,173,170,58,195,165,89,61,14,213,39,133,57,82,61,66,15,165,124,147,174,6,2,4,18,0,62,100,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:57:49 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":500,"statusText":"Internal Server Error"},"service":"telegram-poller","stack":"AxiosError: Request failed with status code 500\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ApiService.uploadPaymentProof (/app/src/services/apiService.js:97:30)\n    at async MessageHandler.handlePhoto (/app/src/handlers/messageHandler.js:49:13)\n    at async TelegramBot.<anonymous> (/app/src/handlers/messageHandler.js:11:13)","status":500,"timestamp":"2025-06-20T01:57:49.579Z"}
{"code":"ERR_BAD_RESPONSE","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------911472405368540101322809","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Error handling photo upload: Request failed with status code 500","name":"AxiosError","request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------911472405368540101322809\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,50,2,1,1,2,2,3,4,4,2,19,2,4,32,44,174,199,221,135,44,226,32,169,64,61,215,78,195,104,92,16,8,183,8,80,21,71,90,109,246,27,235,228,100,61,12,4,48,77,167,211,66,47,46,202,54,212,44,187,182,16,194,20,201,19,214,13,132,93,200,172,88,241,43,11,7,123,143,68,126,205,137,197,100,109,136,180,209,127,138,159,134,75,251,54,154,161,6,2,4,104,84,192,28,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,159,43,52,34,221,127,72,201,190,135,23,134,17,254,108,5,251,33,220,0,99,84,90,120,232,60,38,225,253,145,234,31,246,206,127,11,9,92,149,140,230,145,40,138,201,242,252,150,11,174,237,182,220,210,158,74,177,112,176,245,217,210,43,38,16,157,127,97,147,124,51,79,64,111,83,101,98,49,84,60,126,88,64,255,216,102,130,155,210,6,112,87,133,94,117,208,36,42,217,106,168,244,147,189,78,131,179,18,153,187,125,99,105,158,56,249,181,204,134,67,17,59,226,69,137,63,143,110,241,217,98,110,240,206,10,107,170,215,39,162,5,208,171,139,75,18,243,78,93,252,119,19,78,125,2,201,47,74,131,231,139,154,94,174,145,72,150,70,3,83,71,222,186,184,39,126,82,139,44,137,91,238,221,9,194,26,248,234,132,218,41,186,5,138,224,155,107,21,48,186,252,5,78,164,47,21,104,32,22,123,81,120,138,50,131,27,239,155,159,183,150,173,170,58,195,165,89,61,14,213,39,133,57,82,61,66,15,165,124,147,174,6,2,4,18,0,62,100,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:57:49 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------911472405368540101322809","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":"Internal server error"},"headers":{"access-control-allow-origin":"*","connection":"close","content-length":"33","content-type":"application/json; charset=utf-8","date":"Fri, 20 Jun 2025 01:57:49 GMT","etag":"W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","x-powered-by":"Express"},"request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------911472405368540101322809\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------911472405368540101322809","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,50,2,1,1,2,2,3,4,4,2,19,2,4,32,44,174,199,221,135,44,226,32,169,64,61,215,78,195,104,92,16,8,183,8,80,21,71,90,109,246,27,235,228,100,61,12,4,48,77,167,211,66,47,46,202,54,212,44,187,182,16,194,20,201,19,214,13,132,93,200,172,88,241,43,11,7,123,143,68,126,205,137,197,100,109,136,180,209,127,138,159,134,75,251,54,154,161,6,2,4,104,84,192,28,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,159,43,52,34,221,127,72,201,190,135,23,134,17,254,108,5,251,33,220,0,99,84,90,120,232,60,38,225,253,145,234,31,246,206,127,11,9,92,149,140,230,145,40,138,201,242,252,150,11,174,237,182,220,210,158,74,177,112,176,245,217,210,43,38,16,157,127,97,147,124,51,79,64,111,83,101,98,49,84,60,126,88,64,255,216,102,130,155,210,6,112,87,133,94,117,208,36,42,217,106,168,244,147,189,78,131,179,18,153,187,125,99,105,158,56,249,181,204,134,67,17,59,226,69,137,63,143,110,241,217,98,110,240,206,10,107,170,215,39,162,5,208,171,139,75,18,243,78,93,252,119,19,78,125,2,201,47,74,131,231,139,154,94,174,145,72,150,70,3,83,71,222,186,184,39,126,82,139,44,137,91,238,221,9,194,26,248,234,132,218,41,186,5,138,224,155,107,21,48,186,252,5,78,164,47,21,104,32,22,123,81,120,138,50,131,27,239,155,159,183,150,173,170,58,195,165,89,61,14,213,39,133,57,82,61,66,15,165,124,147,174,6,2,4,18,0,62,100,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:57:49 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":500,"statusText":"Internal Server Error"},"service":"telegram-poller","stack":"AxiosError: Request failed with status code 500\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ApiService.uploadPaymentProof (/app/src/services/apiService.js:97:30)\n    at async MessageHandler.handlePhoto (/app/src/handlers/messageHandler.js:49:13)\n    at async TelegramBot.<anonymous> (/app/src/handlers/messageHandler.js:11:13)","status":500,"timestamp":"2025-06-20T01:57:49.598Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:58:01 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:58:01.506Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:58:18 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:58:18.856Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:58:36 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:58:36.468Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:58:53 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:58:53.777Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:59:11 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:59:11.770Z"}
{"code":"ERR_BAD_RESPONSE","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------590421837881487933131766","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Failed to upload payment proof: Request failed with status code 500","name":"AxiosError","request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------590421837881487933131766\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,51,2,1,1,2,2,3,4,4,2,19,2,4,32,14,252,63,254,149,162,69,92,32,190,241,179,132,114,238,171,74,238,191,222,73,56,76,159,185,237,177,149,133,185,71,38,4,48,163,131,53,218,98,155,64,255,129,33,185,55,190,207,195,126,102,236,190,214,18,187,90,244,105,123,214,179,19,176,130,105,132,198,117,211,80,213,111,136,185,31,85,183,160,254,254,47,161,6,2,4,104,84,192,116,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,220,241,230,240,4,234,100,135,57,215,83,49,255,7,62,174,241,209,89,186,78,139,127,23,86,241,17,96,82,83,140,7,134,78,230,121,30,138,125,16,82,162,242,47,76,228,223,14,116,5,248,93,241,99,156,124,71,3,89,57,193,187,207,2,229,243,135,211,64,112,140,218,101,244,196,44,112,111,95,247,169,37,36,179,39,46,150,233,4,187,224,157,48,118,97,187,122,184,20,179,14,171,71,207,196,212,224,49,184,197,228,10,169,76,114,244,11,239,91,89,203,230,199,29,24,154,75,209,100,247,22,197,226,54,199,181,171,84,149,106,4,60,108,98,199,252,216,187,233,42,213,224,104,66,97,114,99,19,184,166,80,31,142,136,222,184,143,206,65,253,125,139,135,132,108,237,208,30,152,62,196,32,63,249,142,142,234,76,60,115,72,167,103,23,231,190,212,220,79,233,223,90,220,184,154,194,82,12,220,64,102,48,31,47,113,165,52,170,32,178,130,29,67,149,34,124,253,143,114,167,24,234,99,26,41,139,133,172,5,133,174,7,2,5,0,225,47,120,116,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:59:16 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------590421837881487933131766","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":"Internal server error"},"headers":{"access-control-allow-origin":"*","connection":"close","content-length":"33","content-type":"application/json; charset=utf-8","date":"Fri, 20 Jun 2025 01:59:16 GMT","etag":"W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","x-powered-by":"Express"},"request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------590421837881487933131766\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,51,2,1,1,2,2,3,4,4,2,19,2,4,32,14,252,63,254,149,162,69,92,32,190,241,179,132,114,238,171,74,238,191,222,73,56,76,159,185,237,177,149,133,185,71,38,4,48,163,131,53,218,98,155,64,255,129,33,185,55,190,207,195,126,102,236,190,214,18,187,90,244,105,123,214,179,19,176,130,105,132,198,117,211,80,213,111,136,185,31,85,183,160,254,254,47,161,6,2,4,104,84,192,116,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,220,241,230,240,4,234,100,135,57,215,83,49,255,7,62,174,241,209,89,186,78,139,127,23,86,241,17,96,82,83,140,7,134,78,230,121,30,138,125,16,82,162,242,47,76,228,223,14,116,5,248,93,241,99,156,124,71,3,89,57,193,187,207,2,229,243,135,211,64,112,140,218,101,244,196,44,112,111,95,247,169,37,36,179,39,46,150,233,4,187,224,157,48,118,97,187,122,184,20,179,14,171,71,207,196,212,224,49,184,197,228,10,169,76,114,244,11,239,91,89,203,230,199,29,24,154,75,209,100,247,22,197,226,54,199,181,171,84,149,106,4,60,108,98,199,252,216,187,233,42,213,224,104,66,97,114,99,19,184,166,80,31,142,136,222,184,143,206,65,253,125,139,135,132,108,237,208,30,152,62,196,32,63,249,142,142,234,76,60,115,72,167,103,23,231,190,212,220,79,233,223,90,220,184,154,194,82,12,220,64,102,48,31,47,113,165,52,170,32,178,130,29,67,149,34,124,253,143,114,167,24,234,99,26,41,139,133,172,5,133,174,7,2,5,0,225,47,120,116,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:59:16 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":500,"statusText":"Internal Server Error"},"service":"telegram-poller","stack":"AxiosError: Request failed with status code 500\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ApiService.uploadPaymentProof (/app/src/services/apiService.js:97:30)\n    at async MessageHandler.handlePhoto (/app/src/handlers/messageHandler.js:49:13)\n    at async TelegramBot.<anonymous> (/app/src/handlers/messageHandler.js:11:13)","status":500,"timestamp":"2025-06-20T01:59:16.806Z"}
{"code":"ERR_BAD_RESPONSE","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------590421837881487933131766","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Error handling photo upload: Request failed with status code 500","name":"AxiosError","request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------590421837881487933131766\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,51,2,1,1,2,2,3,4,4,2,19,2,4,32,14,252,63,254,149,162,69,92,32,190,241,179,132,114,238,171,74,238,191,222,73,56,76,159,185,237,177,149,133,185,71,38,4,48,163,131,53,218,98,155,64,255,129,33,185,55,190,207,195,126,102,236,190,214,18,187,90,244,105,123,214,179,19,176,130,105,132,198,117,211,80,213,111,136,185,31,85,183,160,254,254,47,161,6,2,4,104,84,192,116,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,220,241,230,240,4,234,100,135,57,215,83,49,255,7,62,174,241,209,89,186,78,139,127,23,86,241,17,96,82,83,140,7,134,78,230,121,30,138,125,16,82,162,242,47,76,228,223,14,116,5,248,93,241,99,156,124,71,3,89,57,193,187,207,2,229,243,135,211,64,112,140,218,101,244,196,44,112,111,95,247,169,37,36,179,39,46,150,233,4,187,224,157,48,118,97,187,122,184,20,179,14,171,71,207,196,212,224,49,184,197,228,10,169,76,114,244,11,239,91,89,203,230,199,29,24,154,75,209,100,247,22,197,226,54,199,181,171,84,149,106,4,60,108,98,199,252,216,187,233,42,213,224,104,66,97,114,99,19,184,166,80,31,142,136,222,184,143,206,65,253,125,139,135,132,108,237,208,30,152,62,196,32,63,249,142,142,234,76,60,115,72,167,103,23,231,190,212,220,79,233,223,90,220,184,154,194,82,12,220,64,102,48,31,47,113,165,52,170,32,178,130,29,67,149,34,124,253,143,114,167,24,234,99,26,41,139,133,172,5,133,174,7,2,5,0,225,47,120,116,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:59:16 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":{"_boundary":"--------------------------590421837881487933131766","_currentStream":null,"_events":{},"_eventsCount":3,"_insideLoop":false,"_overheadLength":178,"_pendingNext":false,"_released":true,"_streams":[],"_valueLength":10,"_valuesToMeasure":[],"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"readable":true,"writable":false},"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://vpn-api:3000/api/orders/4/payment-proof","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":"Internal server error"},"headers":{"access-control-allow-origin":"*","connection":"close","content-length":"33","content-type":"application/json; charset=utf-8","date":"Fri, 20 Jun 2025 01:59:16 GMT","etag":"W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","x-powered-by":"Express"},"request":{"_closed":false,"_contentLength":"244","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/orders/4/payment-proof HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: multipart/form-data; boundary=--------------------------590421837881487933131766\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 244\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: vpn-api:3000\r\nConnection: close\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://vpn-api:3000/api/orders/4/payment-proof","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"244","Content-Type":"multipart/form-data; boundary=--------------------------590421837881487933131766","User-Agent":"axios/1.10.0"},"hostname":"vpn-api","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["api.telegram.org:443:::::::::::::::::::::"],"map":{"api.telegram.org:443:::::::::::::::::::::":{"data":[48,130,8,51,2,1,1,2,2,3,4,4,2,19,2,4,32,14,252,63,254,149,162,69,92,32,190,241,179,132,114,238,171,74,238,191,222,73,56,76,159,185,237,177,149,133,185,71,38,4,48,163,131,53,218,98,155,64,255,129,33,185,55,190,207,195,126,102,236,190,214,18,187,90,244,105,123,214,179,19,176,130,105,132,198,117,211,80,213,111,136,185,31,85,183,160,254,254,47,161,6,2,4,104,84,192,116,162,4,2,2,28,32,163,130,6,160,48,130,6,156,48,130,5,132,160,3,2,1,2,2,8,69,114,231,49,133,164,67,187,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,129,180,49,11,48,9,6,3,85,4,6,19,2,85,83,49,16,48,14,6,3,85,4,8,19,7,65,114,105,122,111,110,97,49,19,48,17,6,3,85,4,7,19,10,83,99,111,116,116,115,100,97,108,101,49,26,48,24,6,3,85,4,10,19,17,71,111,68,97,100,100,121,46,99,111,109,44,32,73,110,99,46,49,45,48,43,6,3,85,4,11,19,36,104,116,116,112,58,47,47,99,101,114,116,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,49,51,48,49,6,3,85,4,3,19,42,71,111,32,68,97,100,100,121,32,83,101,99,117,114,101,32,67,101,114,116,105,102,105,99,97,116,101,32,65,117,116,104,111,114,105,116,121,32,45,32,71,50,48,30,23,13,50,53,48,51,50,53,49,51,48,57,52,49,90,23,13,50,54,48,52,50,54,49,51,48,57,52,49,90,48,27,49,25,48,23,6,3,85,4,3,19,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,180,163,22,158,92,87,201,137,101,237,234,120,11,174,138,88,47,174,90,200,110,73,141,252,87,165,152,136,120,46,11,60,64,60,33,46,154,148,152,51,167,227,66,167,133,250,208,115,132,1,28,114,57,55,35,181,86,29,67,165,113,20,8,36,165,57,204,222,88,83,148,142,42,66,167,78,45,7,50,158,186,139,211,42,169,158,192,227,206,154,16,150,69,88,122,199,30,69,20,35,146,187,84,130,136,148,73,182,190,129,33,0,41,109,201,206,139,57,58,220,53,21,217,235,71,156,239,186,9,14,22,228,217,235,114,48,250,73,171,152,49,124,179,172,43,41,145,135,8,65,114,94,53,199,135,4,34,245,72,118,48,109,136,223,242,165,41,19,112,179,135,2,213,107,88,177,232,115,199,228,239,121,134,164,7,95,103,180,121,141,164,37,1,130,140,224,48,23,203,75,92,251,235,76,18,81,185,201,4,31,126,210,248,186,245,53,141,138,28,55,130,240,21,115,0,110,61,28,118,139,1,116,129,61,228,44,167,204,47,102,220,68,168,39,63,234,208,167,168,241,203,234,218,7,56,189,2,3,1,0,1,163,130,3,72,48,130,3,68,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,57,6,3,85,29,31,4,50,48,48,48,46,160,44,160,42,134,40,104,116,116,112,58,47,47,99,114,108,46,103,111,100,97,100,100,121,46,99,111,109,47,103,100,105,103,50,115,49,45,52,50,50,56,54,46,99,114,108,48,93,6,3,85,29,32,4,86,48,84,48,72,6,11,96,134,72,1,134,253,109,1,7,23,1,48,57,48,55,6,8,43,6,1,5,5,7,2,1,22,43,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,8,6,6,103,129,12,1,2,1,48,118,6,8,43,6,1,5,5,7,1,1,4,106,48,104,48,36,6,8,43,6,1,5,5,7,48,1,134,24,104,116,116,112,58,47,47,111,99,115,112,46,103,111,100,97,100,100,121,46,99,111,109,47,48,64,6,8,43,6,1,5,5,7,48,2,134,52,104,116,116,112,58,47,47,99,101,114,116,105,102,105,99,97,116,101,115,46,103,111,100,97,100,100,121,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,103,100,105,103,50,46,99,114,116,48,31,6,3,85,29,35,4,24,48,22,128,20,64,194,189,39,142,204,52,131,48,162,51,215,251,108,179,240,180,44,128,206,48,49,6,3,85,29,17,4,42,48,40,130,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,130,20,119,119,119,46,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,48,29,6,3,85,29,14,4,22,4,20,5,20,208,192,195,239,14,148,187,33,153,231,163,54,92,40,119,48,150,213,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,118,0,14,87,148,188,243,174,169,62,51,27,44,153,7,179,247,144,223,155,194,61,113,50,37,221,33,169,37,172,97,197,78,33,0,0,1,149,205,108,79,126,0,0,4,3,0,71,48,69,2,33,0,206,192,143,51,196,28,221,92,93,122,41,106,158,18,219,81,248,36,65,198,196,155,110,133,228,52,12,185,166,26,10,111,2,32,86,192,96,84,136,229,23,191,123,209,66,181,8,35,86,81,115,116,226,174,92,21,69,95,7,50,71,8,78,231,181,197,0,117,0,100,17,196,108,164,18,236,167,137,28,162,2,46,0,188,171,79,40,7,212,30,53,39,171,234,254,213,3,201,125,205,240,0,0,1,149,205,108,80,63,0,0,4,3,0,70,48,68,2,32,68,110,60,118,153,30,174,3,249,204,76,67,244,232,161,137,235,187,254,20,214,43,104,117,229,20,32,253,159,126,62,20,2,32,39,7,219,109,209,123,84,234,234,154,13,159,78,116,180,63,150,130,27,246,148,231,210,68,246,54,214,194,188,213,62,126,0,119,0,203,56,247,21,137,124,132,161,68,95,91,193,221,251,201,110,242,154,89,205,71,10,105,5,133,176,203,20,195,20,88,231,0,0,1,149,205,108,80,211,0,0,4,3,0,72,48,70,2,33,0,232,62,80,146,3,79,88,95,235,34,241,75,226,12,57,132,205,68,190,29,71,49,139,144,60,243,162,88,157,251,178,63,2,33,0,195,171,187,89,87,84,35,99,180,162,116,129,235,113,16,221,116,64,62,216,98,249,250,126,128,15,211,93,171,8,53,184,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,151,231,13,211,33,160,138,136,114,57,147,228,176,82,105,66,187,161,141,157,35,39,202,234,135,90,55,11,11,227,59,36,79,134,97,44,237,12,132,42,35,73,77,148,130,155,115,217,64,74,198,21,199,82,114,55,131,127,184,184,129,18,140,23,178,205,72,212,185,141,246,28,230,233,216,249,188,241,78,94,242,227,155,201,135,163,121,87,42,29,161,87,91,57,7,13,90,21,157,61,67,97,165,183,133,248,231,129,225,101,138,51,184,176,110,40,92,49,121,138,127,246,66,117,147,35,129,206,39,3,104,22,224,26,75,169,207,198,98,101,19,168,138,143,209,174,157,69,106,11,142,78,41,52,190,91,250,171,122,174,210,21,71,15,110,189,178,181,0,56,67,216,145,108,145,107,43,50,55,28,172,200,166,39,42,74,191,190,165,64,170,144,227,84,217,78,242,94,12,153,103,70,125,201,105,104,211,66,192,111,25,247,176,244,205,44,91,1,70,64,232,211,200,83,185,192,252,253,169,83,176,104,97,210,205,36,13,110,250,1,214,160,141,192,29,169,60,65,187,84,64,151,200,135,250,121,164,2,4,0,166,18,4,16,97,112,105,46,116,101,108,101,103,114,97,109,46,111,114,103,169,4,2,2,2,88,170,129,243,4,129,240,220,241,230,240,4,234,100,135,57,215,83,49,255,7,62,174,241,209,89,186,78,139,127,23,86,241,17,96,82,83,140,7,134,78,230,121,30,138,125,16,82,162,242,47,76,228,223,14,116,5,248,93,241,99,156,124,71,3,89,57,193,187,207,2,229,243,135,211,64,112,140,218,101,244,196,44,112,111,95,247,169,37,36,179,39,46,150,233,4,187,224,157,48,118,97,187,122,184,20,179,14,171,71,207,196,212,224,49,184,197,228,10,169,76,114,244,11,239,91,89,203,230,199,29,24,154,75,209,100,247,22,197,226,54,199,181,171,84,149,106,4,60,108,98,199,252,216,187,233,42,213,224,104,66,97,114,99,19,184,166,80,31,142,136,222,184,143,206,65,253,125,139,135,132,108,237,208,30,152,62,196,32,63,249,142,142,234,76,60,115,72,167,103,23,231,190,212,220,79,233,223,90,220,184,154,194,82,12,220,64,102,48,31,47,113,165,52,170,32,178,130,29,67,149,34,124,253,143,114,167,24,234,99,26,41,139,133,172,5,133,174,7,2,5,0,225,47,120,116,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/orders/4/payment-proof","pathname":"/api/orders/4/payment-proof","port":"3000","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":244,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":true,"defaultEncoding":"utf8","destroyed":false,"emitClose":true,"ended":false,"ending":false,"errorEmitted":false,"errored":null,"finalCalled":false,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":0,"prefinished":false,"sync":true,"writecb":null,"writelen":0,"writing":false}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":false,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"noDelay":true,"path":null},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"vpn-api:3000:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"vpn-api","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/orders/4/payment-proof","protocol":"http:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":456758},"aborted":false,"client":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["X-Powered-By","Express","Access-Control-Allow-Origin","*","Content-Type","application/json; charset=utf-8","Content-Length","33","ETag","W/\"21-u8tno/8IdqEY6PFcopkQe0syfE4\"","Date","Fri, 20 Jun 2025 01:59:16 GMT","Connection","close"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"http://vpn-api:3000/api/orders/4/payment-proof","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"statusCode":500,"statusMessage":"Internal Server Error","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":false,"socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null]},"_eventsCount":7,"_hadError":false,"_host":"vpn-api","_httpMessage":"[Circular]","_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":{"head":null,"length":0,"tail":null},"decoder":null,"defaultEncoding":"utf8","encoding":null,"errored":null,"flowing":true,"highWaterMark":16384,"length":0,"pipes":[],"state":266328},"_server":null,"_sockname":null,"_writableState":{"afterWriteTickInfo":null,"allBuffers":true,"allNoop":true,"autoDestroy":true,"bufferProcessing":false,"buffered":[],"bufferedIndex":0,"closeEmitted":false,"closed":false,"constructed":true,"corked":0,"decodeStrings":false,"defaultEncoding":"utf8","destroyed":false,"emitClose":false,"ended":true,"ending":true,"errorEmitted":false,"errored":null,"finalCalled":true,"finished":false,"highWaterMark":16384,"length":0,"needDrain":false,"objectMode":false,"pendingcb":1,"prefinished":false,"sync":false,"writecb":null,"writelen":0,"writing":false},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null},"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":500,"statusText":"Internal Server Error"},"service":"telegram-poller","stack":"AxiosError: Request failed with status code 500\n    at settle (/app/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/app/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/app/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ApiService.uploadPaymentProof (/app/src/services/apiService.js:97:30)\n    at async MessageHandler.handlePhoto (/app/src/handlers/messageHandler.js:49:13)\n    at async TelegramBot.<anonymous> (/app/src/handlers/messageHandler.js:11:13)","status":500,"timestamp":"2025-06-20T01:59:16.833Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:59:29 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:59:29.084Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:59:46 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:59:46.287Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 02:00:03 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T02:00:03.192Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 02:00:20 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T02:00:20.099Z"}
