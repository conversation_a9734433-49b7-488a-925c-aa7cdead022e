{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:30:38.565Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:30:44 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:30:44.223Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:30:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:30:54.764Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:31:07 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:31:07.723Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:31:23 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:31:23.484Z"}
{"level":"info","message":"Telegram bot started successfully","service":"telegram-poller","timestamp":"2025-06-20T01:32:27.918Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:32:37 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:32:37.628Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:32:48 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:32:48.962Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:00 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:00.437Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:17 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:17.767Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:37 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:37.466Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:33:54 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:33:54.834Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:11 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:11.996Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:29 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:29.281Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:34:46 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:34:46.562Z"}
{"code":"EFATAL","level":"error","message":"Polling error: EFATAL: Error: read ETIMEDOUT","service":"telegram-poller","stack":"RequestError: Error: read ETIMEDOUT\n    at new RequestError (/app/node_modules/request-promise-core/lib/errors.js:14:15)\n    at plumbing.callback (/app/node_modules/request-promise-core/lib/plumbing.js:87:29)\n    at Request.RP$callback [as _callback] (/app/node_modules/request-promise-core/lib/plumbing.js:46:31)\n    at self.callback (/app/node_modules/@cypress/request/request.js:183:22)\n    at Request.emit (node:events:517:28)\n    at Request.onRequestError (/app/node_modules/@cypress/request/request.js:869:8)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-20T01:35:06.598Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:21 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:21.308Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:38 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:38.431Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:35:55 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:35:55.491Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:36:12 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:36:12.716Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:36:55 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:36:55.318Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:12 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:12.763Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:30 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:30.220Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:37:47 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:37:47.588Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:04 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:04.839Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:21 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:21.923Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:38 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:38.932Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:38:56 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:38:56.163Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:13 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:13.441Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:30 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:30.609Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:39:47 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:39:47.804Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:04 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:04.958Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:22 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:22.209Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:39 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:39.411Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:40:56 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:40:56.705Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:13 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:13.914Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:31 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:31.518Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:41:48 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:41:48.747Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:05 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:06.014Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:23 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:23.346Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:40 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:40.928Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:42:58 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:42:58.275Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:15 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:15.428Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:32 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:32.675Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:43:49 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:43:49.954Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:07 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:07.147Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:24 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:24.674Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"close","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:42 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:42.155Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:44:59 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:44:59.906Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:45:17 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":19,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:45:17.333Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:00 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:00.776Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:18 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:18.246Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:35 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:35.601Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:46:53 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:46:53.169Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:10 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:10.747Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:28 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:28.167Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:47:45 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:47:45.512Z"}
{"code":"ETELEGRAM","level":"error","message":"Polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","response":{"body":{"description":"Conflict: terminated by other getUpdates request; make sure that only one bot instance is running","error_code":409,"ok":false},"headers":{"access-control-allow-origin":"*","access-control-expose-headers":"Content-Length,Content-Type,Date,Server,Connection","connection":"keep-alive","content-length":"143","content-type":"application/json","date":"Fri, 20 Jun 2025 01:48:02 GMT","server":"nginx/1.18.0","strict-transport-security":"max-age=31536000; includeSubDomains; preload"},"request":{"headers":{"content-length":27,"content-type":"application/x-www-form-urlencoded"},"method":"POST","uri":{"auth":null,"hash":null,"host":"api.telegram.org","hostname":"api.telegram.org","href":"https://api.telegram.org/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","path":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","pathname":"/bot5386761715:AAGKkaizk5K9D8uinV7omHFHj8zS65Lmuvk/getUpdates","port":443,"protocol":"https:","query":null,"search":null,"slashes":true}},"statusCode":409},"service":"telegram-poller","stack":"Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running\n    at /app/node_modules/node-telegram-bot-api/src/telegram.js:299:15\n    at tryCatcher (/app/node_modules/bluebird/js/release/util.js:16:23)\n    at Promise._settlePromiseFromHandler (/app/node_modules/bluebird/js/release/promise.js:547:31)\n    at Promise._settlePromise (/app/node_modules/bluebird/js/release/promise.js:604:18)\n    at Promise._settlePromise0 (/app/node_modules/bluebird/js/release/promise.js:649:10)\n    at Promise._settlePromises (/app/node_modules/bluebird/js/release/promise.js:729:18)\n    at _drainQueueStep (/app/node_modules/bluebird/js/release/async.js:93:12)\n    at _drainQueue (/app/node_modules/bluebird/js/release/async.js:86:9)\n    at Async._drainQueues (/app/node_modules/bluebird/js/release/async.js:102:5)\n    at Async.drainQueues [as _onImmediate] (/app/node_modules/bluebird/js/release/async.js:15:14)\n    at process.processImmediate (node:internal/timers:476:21)","timestamp":"2025-06-20T01:48:02.750Z"}
