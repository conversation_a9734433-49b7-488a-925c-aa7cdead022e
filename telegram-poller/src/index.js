const TelegramBot = require('node-telegram-bot-api');
require('dotenv').config();

const logger = require('./utils/logger');
const commandHandler = require('./handlers/commandHandler');
const callbackHandler = require('./handlers/callbackHandler');
const messageHandler = require('./handlers/messageHandler');

const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const ADMIN_CHAT_ID = process.env.TELEGRAM_ADMIN_CHAT_ID;

if (!BOT_TOKEN) {
    logger.error('TELEGRAM_BOT_TOKEN is required');
    process.exit(1);
}

// Create bot instance
const bot = new TelegramBot(BOT_TOKEN, { polling: true });

// Set up handlers
commandHandler.setup(bot);
callbackHandler.setup(bot);
messageHandler.setup(bot);

// Error handling
bot.on('error', (error) => {
    logger.error('Bot error:', error);
});

bot.on('polling_error', (error) => {
    logger.error('Polling error:', error);
});

// Start message
bot.onText(/\/start/, (msg) => {
    const chatId = msg.chat.id;
    const welcomeMessage = `
🌐 *Selamat datang di VPN Service Bot!*

Layanan VPN yang tersedia:
• 🌍 *VPN Internet* - Akses internet umum
• 🎮 *VPN Game* - Khusus gaming dengan routing optimal  
• 🖥️ *VPN Remote* - Dengan port forwarding (Winbox, API, Web)

Gunakan /menu untuk melihat semua pilihan layanan.

_Bot ini melayani pemesanan VPN 24/7_
    `;
    
    bot.sendMessage(chatId, welcomeMessage, { parse_mode: 'Markdown' });
});

logger.info('Telegram bot started successfully');

// Export bot for use in other modules
module.exports = bot;
