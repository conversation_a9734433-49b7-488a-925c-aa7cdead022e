# Testing Guide

Panduan lengkap untuk testing VPN Service Platform.

## 🧪 Testing Overview

Platform ini telah diuji dan ber<PERSON>lan normal dengan semua komponen terintegrasi:

- ✅ Database initialization
- ✅ API endpoints
- ✅ Telegram bot
- ✅ Admin panel
- ✅ Mikrotik API
- ✅ Auto provisioning
- ✅ Docker compose setup

## 🚀 Quick Test

### 1. Start Services
```bash
docker compose up -d
```

### 2. Verify All Services Running
```bash
docker compose ps
```
Expected output: All services should be "Up" status.

### 3. Test API Endpoints
```bash
# VPN API health check
curl http://localhost:3000/health
# Expected: {"status":"OK","service":"vpn-api"}

# Mikrotik API health check  
curl http://localhost:3003/health
# Expected: {"status":"OK","service":"mikrotik-api"}

# Get VPN plans
curl http://localhost:3000/api/plans
# Expected: JSON array with 6 default plans
```

### 4. Test Admin Panel
- Open http://localhost:3002
- Should load React admin panel
- Navigate through different sections

## 🔧 Component Testing

### Database Testing
```bash
# Connect to database
docker compose exec postgres psql -U vpn_user -d vpn_service

# Check tables exist
\dt

# Verify default data
SELECT * FROM vpn_plans;
SELECT COUNT(*) FROM vpn_plans; -- Should return 6

# Exit
\q
```

### VPN API Testing
```bash
# Test all main endpoints
curl http://localhost:3000/api/plans
curl http://localhost:3000/api/orders
curl http://localhost:3000/api/users
curl http://localhost:3000/api/chr
curl http://localhost:3000/api/vpn-accounts
```

### Mikrotik API Testing
```bash
# Test connection endpoint (will fail without real CHR)
curl -X POST http://localhost:3003/api/mikrotik/test-connection \
  -H "Content-Type: application/json" \
  -d '{
    "host": "***********",
    "port": 8728,
    "username": "admin",
    "password": "password"
  }'
```

### Telegram Bot Testing
1. Message your bot with `/start`
2. Use `/menu` to see options
3. Try ordering a VPN plan
4. Test payment proof upload

## 📊 Admin Panel Testing

### Dashboard
- ✅ Statistics cards display
- ✅ Recent orders table
- ✅ Data loads correctly

### Orders Management
- ✅ Orders list with filters
- ✅ Order details modal
- ✅ Payment confirmation
- ✅ Order cancellation
- ✅ Payment proof viewing

### CHR Devices
- ✅ Device list
- ✅ Add new device
- ✅ Edit device
- ✅ Test connection
- ✅ Delete device

### VPN Plans
- ✅ Plans list
- ✅ Add new plan
- ✅ Edit plan
- ✅ Delete plan
- ✅ Plan activation/deactivation

### VPN Accounts
- ✅ Accounts list with filters
- ✅ Account details
- ✅ Status management
- ✅ Account extension
- ✅ Port forwarding info

### Users
- ✅ Users list
- ✅ User details
- ✅ Order history
- ✅ VPN accounts per user

## 🔄 Integration Testing

### Complete Order Flow
1. **User creates order via Telegram**
   ```bash
   # Simulate user creation
   curl -X POST http://localhost:3000/api/users \
     -H "Content-Type: application/json" \
     -d '{
       "telegram_id": *********,
       "username": "testuser",
       "first_name": "Test",
       "last_name": "User"
     }'
   ```

2. **Create order**
   ```bash
   curl -X POST http://localhost:3000/api/orders \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": 1,
       "plan_id": 1
     }'
   ```

3. **Upload payment proof** (via admin panel or API)

4. **Admin confirms payment** (via admin panel)

5. **System auto-provisions** (requires CHR device)

### CHR Integration Testing
1. Add CHR device in admin panel
2. Test connection
3. Create test order
4. Confirm payment
5. Verify auto provisioning

## 🐛 Error Testing

### Database Errors
```bash
# Stop database
docker compose stop postgres

# Try API call (should fail gracefully)
curl http://localhost:3000/api/plans

# Restart database
docker compose start postgres
```

### Service Dependencies
```bash
# Stop VPN API
docker compose stop vpn-api

# Try admin panel (should show connection errors)
# Try Telegram bot (should fail API calls)

# Restart VPN API
docker compose start vpn-api
```

## 📈 Performance Testing

### Load Testing
```bash
# Install apache bench
sudo apt-get install apache2-utils

# Test API performance
ab -n 100 -c 10 http://localhost:3000/api/plans

# Test admin panel
ab -n 50 -c 5 http://localhost:3002/
```

### Database Performance
```bash
# Connect to database
docker compose exec postgres psql -U vpn_user -d vpn_service

# Check query performance
EXPLAIN ANALYZE SELECT * FROM orders 
JOIN users ON orders.user_id = users.id 
JOIN vpn_plans ON orders.plan_id = vpn_plans.id;
```

## 🔍 Monitoring Tests

### Log Verification
```bash
# Check all service logs
docker compose logs --tail=50

# Check for errors
docker compose logs | grep -i error

# Check specific service
docker compose logs vpn-api --tail=20
```

### Health Checks
```bash
# Create health check script
cat > health_check.sh << 'EOF'
#!/bin/bash
echo "Checking VPN API..."
curl -f http://localhost:3000/health || exit 1

echo "Checking Mikrotik API..."
curl -f http://localhost:3003/health || exit 1

echo "Checking Admin Panel..."
curl -f http://localhost:3002/ || exit 1

echo "All services healthy!"
EOF

chmod +x health_check.sh
./health_check.sh
```

## 🧹 Cleanup Testing

### Reset Environment
```bash
# Stop all services
docker compose down

# Remove volumes (reset database)
docker compose down -v

# Rebuild and start
docker compose up -d --build
```

### Verify Clean State
```bash
# Check database is reset
docker compose exec postgres psql -U vpn_user -d vpn_service -c "SELECT COUNT(*) FROM orders;"
# Should return 0

# Check default plans exist
docker compose exec postgres psql -U vpn_user -d vpn_service -c "SELECT COUNT(*) FROM vpn_plans;"
# Should return 6
```

## ✅ Test Checklist

### Pre-deployment
- [ ] All services start successfully
- [ ] Database initializes with default data
- [ ] API endpoints respond correctly
- [ ] Admin panel loads and functions
- [ ] Telegram bot responds to commands
- [ ] CHR device can be added and tested
- [ ] Order flow works end-to-end
- [ ] Auto provisioning triggers correctly
- [ ] Logs show no critical errors

### Post-deployment
- [ ] Monitor service health
- [ ] Check database performance
- [ ] Verify user registrations
- [ ] Monitor order processing
- [ ] Check CHR device connectivity
- [ ] Verify payment confirmations
- [ ] Monitor VPN account creation

## 🚨 Known Issues & Workarounds

### Port Conflicts
If PostgreSQL port 5433 is in use:
```bash
# Change port in docker-compose.yml
ports:
  - "5434:5432"  # Use different external port
```

### Memory Issues
If services fail to start due to memory:
```bash
# Check available memory
free -h

# Restart Docker
sudo systemctl restart docker

# Start services one by one
docker compose up -d postgres
docker compose up -d vpn-api
docker compose up -d mikrotik-api
docker compose up -d telegram-poller
docker compose up -d frontend-admin
```

### Network Issues
If services can't communicate:
```bash
# Check Docker network
docker network ls
docker network inspect vpn-manager_vpn-network

# Recreate network
docker compose down
docker compose up -d
```

---

**Testing completed successfully! ✅**

All components are working normally and ready for production use.
