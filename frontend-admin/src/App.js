import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import Orders from './pages/Orders';
import VpnAccounts from './pages/VpnAccounts';
import ChrDevices from './pages/ChrDevices';
import VpnPlans from './pages/VpnPlans';
import Users from './pages/Users';

const { Content } = Layout;

function App() {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sidebar />
      <Layout>
        <Content style={{ margin: '24px 16px', padding: 24, background: '#fff' }}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/orders" element={<Orders />} />
            <Route path="/vpn-accounts" element={<VpnAccounts />} />
            <Route path="/chr-devices" element={<ChrDevices />} />
            <Route path="/vpn-plans" element={<VpnPlans />} />
            <Route path="/users" element={<Users />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
