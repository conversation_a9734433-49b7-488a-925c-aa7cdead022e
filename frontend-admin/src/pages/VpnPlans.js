import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber,
  Select,
  Switch,
  message, 
  Space,
  Tag,
  Typography,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { plansApi } from '../services/api';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const VpnPlans = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await plansApi.getAll();
      setPlans(response.data);
    } catch (error) {
      message.error('Failed to fetch VPN plans');
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingPlan(null);
    form.resetFields();
    form.setFieldsValue({ duration_days: 30, is_active: true });
    setModalVisible(true);
  };

  const handleEdit = (plan) => {
    setEditingPlan(plan);
    form.setFieldsValue(plan);
    setModalVisible(true);
  };

  const handleDelete = async (planId) => {
    try {
      await plansApi.delete(planId);
      message.success('VPN plan deleted successfully');
      fetchPlans();
    } catch (error) {
      message.error('Failed to delete VPN plan');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingPlan) {
        await plansApi.update(editingPlan.id, values);
        message.success('VPN plan updated successfully');
      } else {
        await plansApi.create(values);
        message.success('VPN plan created successfully');
      }
      setModalVisible(false);
      fetchPlans();
    } catch (error) {
      message.error(editingPlan ? 'Failed to update VPN plan' : 'Failed to create VPN plan');
    }
  };

  const getTypeColor = (type) => {
    const colors = {
      internet: 'blue',
      game: 'green',
      remote: 'purple',
    };
    return colors[type] || 'default';
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={getTypeColor(type)}>
          {type.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      render: (price) => `Rp ${Number(price).toLocaleString('id-ID')}`,
    },
    {
      title: 'Bandwidth',
      dataIndex: 'bandwidth_limit',
      key: 'bandwidth_limit',
    },
    {
      title: 'Duration',
      dataIndex: 'duration_days',
      key: 'duration_days',
      render: (days) => `${days} days`,
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="default"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this VPN plan?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>VPN Plans Management</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            Add VPN Plan
          </Button>
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={fetchPlans}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={plans}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} plans`,
          }}
        />
      </Card>

      <Modal
        title={editingPlan ? 'Edit VPN Plan' : 'Add VPN Plan'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Plan Name"
            rules={[{ required: true, message: 'Please enter plan name' }]}
          >
            <Input placeholder="e.g., VPN Internet Basic" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Plan Type"
            rules={[{ required: true, message: 'Please select plan type' }]}
          >
            <Select placeholder="Select plan type">
              <Option value="internet">Internet</Option>
              <Option value="game">Game</Option>
              <Option value="remote">Remote</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="price"
            label="Price (IDR)"
            rules={[{ required: true, message: 'Please enter price' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `Rp ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/Rp\s?|(,*)/g, '')}
              placeholder="Enter price in IDR"
            />
          </Form.Item>

          <Form.Item
            name="bandwidth_limit"
            label="Bandwidth Limit"
            rules={[{ required: true, message: 'Please enter bandwidth limit' }]}
          >
            <Input placeholder="e.g., 10M/10M, 20M/20M" />
          </Form.Item>

          <Form.Item
            name="duration_days"
            label="Duration (Days)"
            rules={[{ required: true, message: 'Please enter duration' }]}
          >
            <InputNumber
              min={1}
              max={365}
              style={{ width: '100%' }}
              placeholder="Enter duration in days"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea
              rows={3}
              placeholder="Enter plan description (optional)"
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingPlan ? 'Update' : 'Create'} Plan
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VpnPlans;
