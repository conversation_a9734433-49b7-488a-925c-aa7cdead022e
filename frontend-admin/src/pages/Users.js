import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Modal, 
  message, 
  Space,
  Typography,
  Descriptions,
  Tag
} from 'antd';
import { 
  EyeOutlined, 
  ReloadOutlined,
  UserOutlined
} from '@ant-design/icons';
import { usersApi, ordersApi, vpnAccountsApi } from '../services/api';

const { Title } = Typography;

const Users = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userOrders, setUserOrders] = useState([]);
  const [userAccounts, setUserAccounts] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await usersApi.getAll();
      setUsers(response.data);
    } catch (error) {
      message.error('Failed to fetch users');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewUser = async (user) => {
    try {
      setSelectedUser(user);
      setModalVisible(true);
      setDetailsLoading(true);

      // Fetch user orders and accounts
      const [ordersRes, accountsRes] = await Promise.all([
        ordersApi.getAll({ user_id: user.id }),
        vpnAccountsApi.getByUser(user.id)
      ]);

      setUserOrders(ordersRes.data);
      setUserAccounts(accountsRes.data);
    } catch (error) {
      message.error('Failed to fetch user details');
    } finally {
      setDetailsLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      paid: 'blue',
      active: 'green',
      expired: 'red',
      cancelled: 'gray',
    };
    return colors[status] || 'default';
  };

  const getAccountStatusColor = (status) => {
    const colors = {
      active: 'green',
      disabled: 'orange',
      expired: 'red',
    };
    return colors[status] || 'default';
  };

  const columns = [
    {
      title: 'Telegram ID',
      dataIndex: 'telegram_id',
      key: 'telegram_id',
      render: (id) => <code>{id}</code>,
    },
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => {
        const name = `${record.first_name || ''} ${record.last_name || ''}`.trim();
        return name || 'N/A';
      },
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      render: (username) => username ? `@${username}` : 'N/A',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone) => phone || 'N/A',
    },
    {
      title: 'Joined',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewUser(record)}
          >
            View Details
          </Button>
        </Space>
      ),
    },
  ];

  const orderColumns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => `#${id}`,
    },
    {
      title: 'Plan',
      dataIndex: ['VpnPlan', 'name'],
      key: 'plan',
    },
    {
      title: 'Amount',
      dataIndex: 'total_amount',
      key: 'amount',
      render: (amount) => `Rp ${Number(amount).toLocaleString('id-ID')}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'created_at',
      key: 'date',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
  ];

  const accountColumns = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      render: (username) => <code>{username}</code>,
    },
    {
      title: 'Plan',
      dataIndex: ['Order', 'VpnPlan', 'name'],
      key: 'plan',
    },
    {
      title: 'CHR Device',
      dataIndex: ['ChrDevice', 'name'],
      key: 'chr_device',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getAccountStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date) => {
        if (!date) return 'N/A';
        const expiryDate = new Date(date);
        const now = new Date();
        const isExpired = expiryDate < now;
        
        return (
          <span style={{ color: isExpired ? 'red' : 'inherit' }}>
            {expiryDate.toLocaleDateString('id-ID')}
          </span>
        );
      },
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>Users Management</Title>
        <Space>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchUsers}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} users`,
          }}
        />
      </Card>

      {/* User Details Modal */}
      <Modal
        title={
          <Space>
            <UserOutlined />
            User Details
          </Space>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={1000}
      >
        {selectedUser && (
          <div>
            <Descriptions bordered column={2} style={{ marginBottom: 24 }}>
              <Descriptions.Item label="Telegram ID">
                <code>{selectedUser.telegram_id}</code>
              </Descriptions.Item>
              <Descriptions.Item label="Username">
                {selectedUser.username ? `@${selectedUser.username}` : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="First Name">
                {selectedUser.first_name || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Last Name">
                {selectedUser.last_name || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Phone">
                {selectedUser.phone || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Joined">
                {new Date(selectedUser.created_at).toLocaleString('id-ID')}
              </Descriptions.Item>
            </Descriptions>

            <Title level={4}>Orders History</Title>
            <Table
              columns={orderColumns}
              dataSource={userOrders}
              rowKey="id"
              loading={detailsLoading}
              pagination={false}
              size="small"
              style={{ marginBottom: 24 }}
            />

            <Title level={4}>VPN Accounts</Title>
            <Table
              columns={accountColumns}
              dataSource={userAccounts}
              rowKey="id"
              loading={detailsLoading}
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Users;
