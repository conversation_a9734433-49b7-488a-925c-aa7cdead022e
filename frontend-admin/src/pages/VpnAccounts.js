import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Tag, 
  Button, 
  Modal, 
  Form, 
  InputNumber,
  message, 
  Space,
  Select,
  Typography,
  Descriptions
} from 'antd';
import { 
  EyeOutlined, 
  ReloadOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { vpnAccountsApi } from '../services/api';

const { Option } = Select;
const { Title } = Typography;

const VpnAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [extendModalVisible, setExtendModalVisible] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [form] = Form.useForm();

  useEffect(() => {
    fetchAccounts();
  }, [statusFilter]);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const params = statusFilter !== 'all' ? { status: statusFilter } : {};
      const response = await vpnAccountsApi.getAll(params);
      setAccounts(response.data);
    } catch (error) {
      message.error('Failed to fetch VPN accounts');
      console.error('Error fetching accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewAccount = (account) => {
    setSelectedAccount(account);
    setModalVisible(true);
  };

  const handleExtendAccount = (account) => {
    setSelectedAccount(account);
    form.resetFields();
    setExtendModalVisible(true);
  };

  const handleStatusChange = async (accountId, newStatus) => {
    try {
      await vpnAccountsApi.updateStatus(accountId, { status: newStatus });
      message.success('Account status updated successfully');
      fetchAccounts();
    } catch (error) {
      message.error('Failed to update account status');
    }
  };

  const handleExtend = async (values) => {
    try {
      await vpnAccountsApi.extend(selectedAccount.id, values);
      message.success('Account extended successfully');
      setExtendModalVisible(false);
      fetchAccounts();
    } catch (error) {
      message.error('Failed to extend account');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'green',
      disabled: 'orange',
      expired: 'red',
    };
    return colors[status] || 'default';
  };

  const getTypeColor = (type) => {
    const colors = {
      internet: 'blue',
      game: 'green',
      remote: 'purple',
    };
    return colors[type] || 'default';
  };

  const columns = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      render: (username) => <code>{username}</code>,
    },
    {
      title: 'User',
      dataIndex: ['Order', 'User'],
      key: 'user',
      render: (user) => {
        if (!user) return 'N/A';
        const name = `${user.first_name || ''} ${user.last_name || ''}`.trim();
        return name || user.username || `ID: ${user.telegram_id}`;
      },
    },
    {
      title: 'Plan',
      dataIndex: ['Order', 'VpnPlan', 'name'],
      key: 'plan',
    },
    {
      title: 'Type',
      dataIndex: ['Order', 'VpnPlan', 'type'],
      key: 'type',
      render: (type) => (
        <Tag color={getTypeColor(type)}>
          {type?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'CHR Device',
      dataIndex: ['ChrDevice', 'name'],
      key: 'chr_device',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Select
          value={status}
          size="small"
          style={{ width: 100 }}
          onChange={(newStatus) => handleStatusChange(record.id, newStatus)}
        >
          <Option value="active">
            <Tag color="green">Active</Tag>
          </Option>
          <Option value="disabled">
            <Tag color="orange">Disabled</Tag>
          </Option>
          <Option value="expired">
            <Tag color="red">Expired</Tag>
          </Option>
        </Select>
      ),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date) => {
        if (!date) return 'N/A';
        const expiryDate = new Date(date);
        const now = new Date();
        const isExpired = expiryDate < now;
        const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
        
        return (
          <span style={{ color: isExpired ? 'red' : daysLeft <= 7 ? 'orange' : 'inherit' }}>
            {expiryDate.toLocaleDateString('id-ID')}
            {!isExpired && daysLeft > 0 && (
              <div style={{ fontSize: '12px' }}>
                ({daysLeft} days left)
              </div>
            )}
          </span>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewAccount(record)}
          >
            View
          </Button>
          <Button
            type="default"
            icon={<ClockCircleOutlined />}
            size="small"
            onClick={() => handleExtendAccount(record)}
          >
            Extend
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>VPN Accounts Management</Title>
        <Space>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">All Status</Option>
            <Option value="active">Active</Option>
            <Option value="disabled">Disabled</Option>
            <Option value="expired">Expired</Option>
          </Select>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchAccounts}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} accounts`,
          }}
        />
      </Card>

      {/* Account Details Modal */}
      <Modal
        title={`VPN Account Details - ${selectedAccount?.username}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAccount && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Username">
                <code>{selectedAccount.username}</code>
              </Descriptions.Item>
              <Descriptions.Item label="Password">
                <code>{selectedAccount.password}</code>
              </Descriptions.Item>
              <Descriptions.Item label="Profile">
                {selectedAccount.profile || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedAccount.status)}>
                  {selectedAccount.status.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="CHR Device">
                {selectedAccount.ChrDevice?.name} ({selectedAccount.ChrDevice?.host})
              </Descriptions.Item>
              <Descriptions.Item label="Plan">
                {selectedAccount.Order?.VpnPlan?.name}
              </Descriptions.Item>
              <Descriptions.Item label="Plan Type">
                <Tag color={getTypeColor(selectedAccount.Order?.VpnPlan?.type)}>
                  {selectedAccount.Order?.VpnPlan?.type?.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="User">
                {selectedAccount.Order?.User ? 
                  `${selectedAccount.Order.User.first_name || ''} ${selectedAccount.Order.User.last_name || ''}`.trim() || 
                  selectedAccount.Order.User.username || 
                  `ID: ${selectedAccount.Order.User.telegram_id}` 
                  : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {new Date(selectedAccount.created_at).toLocaleString('id-ID')}
              </Descriptions.Item>
              <Descriptions.Item label="Expires">
                {selectedAccount.expires_at ? 
                  new Date(selectedAccount.expires_at).toLocaleString('id-ID') : 
                  'N/A'}
              </Descriptions.Item>
            </Descriptions>

            {/* Port Forwarding for Remote VPN */}
            {selectedAccount.Order?.VpnPlan?.type === 'remote' && selectedAccount.PortForwards?.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={4}>Port Forwarding</Title>
                <Table
                  size="small"
                  dataSource={selectedAccount.PortForwards.filter(pf => pf.is_active)}
                  columns={[
                    {
                      title: 'Service',
                      dataIndex: 'service_type',
                      key: 'service_type',
                      render: (type) => type.charAt(0).toUpperCase() + type.slice(1),
                    },
                    {
                      title: 'External Port',
                      dataIndex: 'external_port',
                      key: 'external_port',
                    },
                    {
                      title: 'Internal Port',
                      dataIndex: 'internal_port',
                      key: 'internal_port',
                    },
                    {
                      title: 'Access URL',
                      key: 'access_url',
                      render: (_, record) => (
                        <code>
                          {selectedAccount.ChrDevice?.host}:{record.external_port}
                        </code>
                      ),
                    },
                  ]}
                  pagination={false}
                />
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Extend Account Modal */}
      <Modal
        title="Extend VPN Account"
        open={extendModalVisible}
        onCancel={() => setExtendModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleExtend} layout="vertical">
          <Form.Item
            name="days"
            label="Extend by (days)"
            rules={[{ required: true, message: 'Please enter number of days' }]}
          >
            <InputNumber
              min={1}
              max={365}
              style={{ width: '100%' }}
              placeholder="Enter number of days to extend"
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Extend Account
              </Button>
              <Button onClick={() => setExtendModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VpnAccounts;
