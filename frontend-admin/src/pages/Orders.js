import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Tag, 
  Button, 
  Modal, 
  Form, 
  Input, 
  message, 
  Space,
  Select,
  Image,
  Typography,
  Descriptions
} from 'antd';
import { 
  EyeOutlined, 
  CheckOutlined, 
  CloseOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import { ordersApi } from '../services/api';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [cancelModalVisible, setCancelModalVisible] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [form] = Form.useForm();

  useEffect(() => {
    fetchOrders();
  }, [statusFilter]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const params = statusFilter !== 'all' ? { status: statusFilter } : {};
      const response = await ordersApi.getAll(params);
      setOrders(response.data);
    } catch (error) {
      message.error('Failed to fetch orders');
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewOrder = async (orderId) => {
    try {
      const response = await ordersApi.getById(orderId);
      setSelectedOrder(response.data);
      setModalVisible(true);
    } catch (error) {
      message.error('Failed to fetch order details');
    }
  };

  const handleConfirmOrder = (order) => {
    setSelectedOrder(order);
    setConfirmModalVisible(true);
  };

  const handleCancelOrder = (order) => {
    setSelectedOrder(order);
    setCancelModalVisible(true);
  };

  const confirmOrder = async (values) => {
    try {
      await ordersApi.confirm(selectedOrder.id, values);
      message.success('Order confirmed successfully');
      setConfirmModalVisible(false);
      fetchOrders();
    } catch (error) {
      message.error('Failed to confirm order');
    }
  };

  const cancelOrder = async (values) => {
    try {
      await ordersApi.cancel(selectedOrder.id, values);
      message.success('Order cancelled successfully');
      setCancelModalVisible(false);
      fetchOrders();
    } catch (error) {
      message.error('Failed to cancel order');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      paid: 'blue',
      active: 'green',
      expired: 'red',
      cancelled: 'gray',
    };
    return colors[status] || 'default';
  };

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => `#${id}`,
    },
    {
      title: 'User',
      dataIndex: ['User'],
      key: 'user',
      render: (user) => {
        if (!user) return 'N/A';
        const name = `${user.first_name || ''} ${user.last_name || ''}`.trim();
        return name || user.username || `ID: ${user.telegram_id}`;
      },
    },
    {
      title: 'Plan',
      dataIndex: ['VpnPlan', 'name'],
      key: 'plan',
    },
    {
      title: 'Type',
      dataIndex: ['VpnPlan', 'type'],
      key: 'type',
      render: (type) => {
        const colors = { internet: 'blue', game: 'green', remote: 'purple' };
        return <Tag color={colors[type]}>{type?.toUpperCase()}</Tag>;
      },
    },
    {
      title: 'Amount',
      dataIndex: 'total_amount',
      key: 'amount',
      render: (amount) => `Rp ${Number(amount).toLocaleString('id-ID')}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'created_at',
      key: 'date',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewOrder(record.id)}
          >
            View
          </Button>
          {record.status === 'paid' && (
            <Button
              type="primary"
              icon={<CheckOutlined />}
              size="small"
              style={{ backgroundColor: '#52c41a' }}
              onClick={() => handleConfirmOrder(record)}
            >
              Confirm
            </Button>
          )}
          {['pending', 'paid'].includes(record.status) && (
            <Button
              danger
              icon={<CloseOutlined />}
              size="small"
              onClick={() => handleCancelOrder(record)}
            >
              Cancel
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>Orders Management</Title>
        <Space>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">All Status</Option>
            <Option value="pending">Pending</Option>
            <Option value="paid">Paid</Option>
            <Option value="active">Active</Option>
            <Option value="expired">Expired</Option>
            <Option value="cancelled">Cancelled</Option>
          </Select>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchOrders}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} orders`,
          }}
        />
      </Card>

      {/* Order Details Modal */}
      <Modal
        title={`Order Details #${selectedOrder?.id}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Order ID">#{selectedOrder.id}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedOrder.status)}>
                  {selectedOrder.status.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="User">
                {selectedOrder.User ? 
                  `${selectedOrder.User.first_name || ''} ${selectedOrder.User.last_name || ''}`.trim() || 
                  selectedOrder.User.username || 
                  `ID: ${selectedOrder.User.telegram_id}` 
                  : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Telegram ID">
                {selectedOrder.User?.telegram_id || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Plan">{selectedOrder.VpnPlan?.name}</Descriptions.Item>
              <Descriptions.Item label="Type">
                <Tag color={selectedOrder.VpnPlan?.type === 'internet' ? 'blue' : 
                           selectedOrder.VpnPlan?.type === 'game' ? 'green' : 'purple'}>
                  {selectedOrder.VpnPlan?.type?.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Amount">
                Rp {Number(selectedOrder.total_amount).toLocaleString('id-ID')}
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {new Date(selectedOrder.created_at).toLocaleString('id-ID')}
              </Descriptions.Item>
              {selectedOrder.confirmed_at && (
                <Descriptions.Item label="Confirmed">
                  {new Date(selectedOrder.confirmed_at).toLocaleString('id-ID')}
                </Descriptions.Item>
              )}
              {selectedOrder.expires_at && (
                <Descriptions.Item label="Expires">
                  {new Date(selectedOrder.expires_at).toLocaleString('id-ID')}
                </Descriptions.Item>
              )}
            </Descriptions>

            {selectedOrder.payment_proof && (
              <div style={{ marginTop: 16 }}>
                <Title level={4}>Payment Proof</Title>
                <Image
                  src={`/uploads/${selectedOrder.payment_proof}`}
                  alt="Payment Proof"
                  style={{ maxWidth: '100%', maxHeight: 400 }}
                />
              </div>
            )}

            {selectedOrder.admin_notes && (
              <div style={{ marginTop: 16 }}>
                <Title level={4}>Admin Notes</Title>
                <p>{selectedOrder.admin_notes}</p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Confirm Order Modal */}
      <Modal
        title="Confirm Order"
        open={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={confirmOrder} layout="vertical">
          <Form.Item
            name="admin_notes"
            label="Admin Notes (Optional)"
          >
            <TextArea rows={4} placeholder="Add any notes about this confirmation..." />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Confirm Order
              </Button>
              <Button onClick={() => setConfirmModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Cancel Order Modal */}
      <Modal
        title="Cancel Order"
        open={cancelModalVisible}
        onCancel={() => setCancelModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={cancelOrder} layout="vertical">
          <Form.Item
            name="admin_notes"
            label="Cancellation Reason"
            rules={[{ required: true, message: 'Please provide a reason for cancellation' }]}
          >
            <TextArea rows={4} placeholder="Explain why this order is being cancelled..." />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button danger htmlType="submit">
                Cancel Order
              </Button>
              <Button onClick={() => setCancelModalVisible(false)}>
                Back
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Orders;
