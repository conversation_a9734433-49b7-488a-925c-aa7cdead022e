import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber,
  message, 
  Space,
  Tag,
  Typography,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { chrApi } from '../services/api';

const { Title } = Typography;

const ChrDevices = () => {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDevice, setEditingDevice] = useState(null);
  const [testingDevice, setTestingDevice] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchDevices();
  }, []);

  const fetchDevices = async () => {
    try {
      setLoading(true);
      const response = await chrApi.getAll();
      setDevices(response.data);
    } catch (error) {
      message.error('Failed to fetch CHR devices');
      console.error('Error fetching devices:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingDevice(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (device) => {
    setEditingDevice(device);
    form.setFieldsValue(device);
    setModalVisible(true);
  };

  const handleDelete = async (deviceId) => {
    try {
      await chrApi.delete(deviceId);
      message.success('CHR device deleted successfully');
      fetchDevices();
    } catch (error) {
      message.error('Failed to delete CHR device');
    }
  };

  const handleTest = async (device) => {
    try {
      setTestingDevice(device.id);
      const response = await chrApi.test(device.id);
      
      if (response.data.success) {
        message.success(`Connection successful! Device: ${response.data.identity}`);
      } else {
        message.error(`Connection failed: ${response.data.error}`);
      }
    } catch (error) {
      message.error('Failed to test connection');
    } finally {
      setTestingDevice(null);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingDevice) {
        await chrApi.update(editingDevice.id, values);
        message.success('CHR device updated successfully');
      } else {
        await chrApi.create(values);
        message.success('CHR device added successfully');
      }
      setModalVisible(false);
      fetchDevices();
    } catch (error) {
      message.error(editingDevice ? 'Failed to update CHR device' : 'Failed to add CHR device');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Host',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: 'Port',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'} icon={isActive ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            loading={testingDevice === record.id}
            onClick={() => handleTest(record)}
          >
            Test
          </Button>
          <Button
            type="default"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this CHR device?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>CHR Devices Management</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            Add CHR Device
          </Button>
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={fetchDevices}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={devices}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} devices`,
          }}
        />
      </Card>

      <Modal
        title={editingDevice ? 'Edit CHR Device' : 'Add CHR Device'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Device Name"
            rules={[{ required: true, message: 'Please enter device name' }]}
          >
            <Input placeholder="e.g., CHR-Server-1" />
          </Form.Item>

          <Form.Item
            name="host"
            label="Host/IP Address"
            rules={[{ required: true, message: 'Please enter host/IP address' }]}
          >
            <Input placeholder="e.g., ************* or chr1.example.com" />
          </Form.Item>

          <Form.Item
            name="port"
            label="API Port"
            rules={[{ required: true, message: 'Please enter API port' }]}
            initialValue={8728}
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="username"
            label="Username"
            rules={[{ required: true, message: 'Please enter username' }]}
          >
            <Input placeholder="Mikrotik username" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: 'Please enter password' }]}
          >
            <Input.Password placeholder="Mikrotik password" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingDevice ? 'Update' : 'Add'} Device
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ChrDevices;
