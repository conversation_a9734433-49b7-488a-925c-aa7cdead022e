import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Typography } from 'antd';
import {
  UserOutlined,
  ShoppingCartOutlined,
  KeyOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { ordersApi, vpnAccountsApi, usersApi, chrApi } from '../services/api';

const { Title } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOrders: 0,
    activeAccounts: 0,
    totalChrDevices: 0,
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch all data in parallel
      const [usersRes, ordersRes, accountsRes, chrRes] = await Promise.all([
        usersApi.getAll(),
        ordersApi.getAll(),
        vpnAccountsApi.getAll(),
        chrApi.getAll(),
      ]);

      // Calculate stats
      setStats({
        totalUsers: usersRes.data.length,
        totalOrders: ordersRes.data.length,
        activeAccounts: accountsRes.data.filter(acc => acc.status === 'active').length,
        totalChrDevices: chrRes.data.filter(chr => chr.is_active).length,
      });

      // Get recent orders (last 10)
      const recent = ordersRes.data
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 10);
      setRecentOrders(recent);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      paid: 'blue',
      active: 'green',
      expired: 'red',
      cancelled: 'gray',
    };
    return colors[status] || 'default';
  };

  const orderColumns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => `#${id}`,
    },
    {
      title: 'User',
      dataIndex: ['User', 'first_name'],
      key: 'user',
      render: (firstName, record) => {
        const user = record.User;
        return `${firstName || ''} ${user?.last_name || ''}`.trim() || user?.username || 'N/A';
      },
    },
    {
      title: 'Plan',
      dataIndex: ['VpnPlan', 'name'],
      key: 'plan',
    },
    {
      title: 'Amount',
      dataIndex: 'total_amount',
      key: 'amount',
      render: (amount) => `Rp ${Number(amount).toLocaleString('id-ID')}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'created_at',
      key: 'date',
      render: (date) => new Date(date).toLocaleDateString('id-ID'),
    },
  ];

  return (
    <div>
      <Title level={2}>Dashboard</Title>
      
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={stats.totalOrders}
              prefix={<ShoppingCartOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active VPN Accounts"
              value={stats.activeAccounts}
              prefix={<KeyOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="CHR Devices"
              value={stats.totalChrDevices}
              prefix={<DatabaseOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      <Card title="Recent Orders" style={{ marginTop: 24 }}>
        <Table
          columns={orderColumns}
          dataSource={recentOrders}
          rowKey="id"
          loading={loading}
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );
};

export default Dashboard;
