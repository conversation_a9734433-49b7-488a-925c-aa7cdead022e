import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 10000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Users API
export const usersApi = {
  getAll: () => api.get('/users'),
  getById: (id) => api.get(`/users/${id}`),
  getByTelegramId: (telegramId) => api.get(`/users/telegram/${telegramId}`),
  create: (data) => api.post('/users', data),
  update: (id, data) => api.put(`/users/${id}`, data),
};

// Orders API
export const ordersApi = {
  getAll: (params) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  create: (data) => api.post('/orders', data),
  confirm: (id, data) => api.post(`/orders/${id}/confirm`, data),
  cancel: (id, data) => api.post(`/orders/${id}/cancel`, data),
  uploadPaymentProof: (id, formData) => 
    api.post(`/orders/${id}/payment-proof`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
};

// VPN Plans API
export const plansApi = {
  getAll: () => api.get('/plans'),
  getByType: (type) => api.get(`/plans/type/${type}`),
  getById: (id) => api.get(`/plans/${id}`),
  create: (data) => api.post('/plans', data),
  update: (id, data) => api.put(`/plans/${id}`, data),
  delete: (id) => api.delete(`/plans/${id}`),
};

// CHR Devices API
export const chrApi = {
  getAll: () => api.get('/chr'),
  getById: (id) => api.get(`/chr/${id}`),
  create: (data) => api.post('/chr', data),
  update: (id, data) => api.put(`/chr/${id}`, data),
  delete: (id) => api.delete(`/chr/${id}`),
  test: (id) => api.post(`/chr/${id}/test`),
};

// VPN Accounts API
export const vpnAccountsApi = {
  getAll: (params) => api.get('/vpn-accounts', { params }),
  getById: (id) => api.get(`/vpn-accounts/${id}`),
  getByUser: (userId) => api.get(`/vpn-accounts/user/${userId}`),
  updateStatus: (id, data) => api.put(`/vpn-accounts/${id}/status`, data),
  extend: (id, data) => api.post(`/vpn-accounts/${id}/extend`, data),
};

export default api;
