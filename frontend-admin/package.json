{"name": "frontend-admin", "version": "1.0.0", "description": "Admin Panel for VPN Service", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "axios": "^1.5.0", "antd": "^5.9.0", "@ant-design/icons": "^5.2.6", "moment": "^2.29.4", "recharts": "^2.8.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000"}