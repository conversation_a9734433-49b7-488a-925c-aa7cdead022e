# VPN Service Platform

Platform penjualan layanan VPN berbasis Mikrotik CHR dengan bot Telegram dan panel admin yang lengkap.

## 🚀 Fitur Utama

### 🤖 Bot Telegram
- Pemesanan VPN melalui bot Telegram
- Upload bukti pembayaran
- Notifikasi real-time
- Manajemen akun VPN user

### 🎛️ Panel Admin
- Dashboard monitoring
- Manajemen pesanan dan konfirmasi pembayaran
- Manajemen CHR devices
- Manajemen paket VPN
- Monitoring akun VPN aktif
- Manajemen user

### 🔧 Auto Provisioning
- Otomatis membuat akun VPN setelah pembayaran dikonfirmasi
- Auto setup port forwarding untuk VPN Remote
- Monitoring expired accounts
- Load balancing antar CHR devices

### 📊 Jenis VPN
- **VPN Internet**: Akses internet umum dengan bandwidth terbatas
- **VPN Game**: Khusus gaming dengan routing optimal
- **VPN Remote**: Dengan port forwarding (Winbox, API, Web)

## 🏗️ Arsitektur

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram Bot  │    │   Admin Panel   │    │  Mikrotik API   │
│   (Port 3001)   │    │   (Port 3002)   │    │   (Port 3003)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────────┐
                    │    VPN API      │
                    │   (Port 3000)   │
                    └─────────┬───────┘
                              │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   (Port 5433)   │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Git

### Installation

1. **Clone repository**
```bash
git clone <repository-url>
cd vpn-manager
```

2. **Setup environment**
```bash
cp .env.example .env
# Edit .env file with your configuration
```

3. **Start services**
```bash
docker compose up -d
```

4. **Verify installation**
```bash
# Check all services are running
docker compose ps

# Test API endpoints
curl http://localhost:3000/health
curl http://localhost:3003/health
```

## 🌐 Access Points

- **Admin Panel**: http://localhost:3002
- **VPN API**: http://localhost:3000
- **Mikrotik API**: http://localhost:3003
- **Database**: localhost:5433

## ⚙️ Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
POSTGRES_DB=vpn_service
POSTGRES_USER=vpn_user
POSTGRES_PASSWORD=vpn_password

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_CHAT_ID=your_admin_chat_id

# API URLs (for internal communication)
VPN_API_URL=http://vpn-api:3000
MIKROTIK_API_URL=http://mikrotik-api:3003

# Frontend
REACT_APP_API_URL=http://localhost:3000
```

### Telegram Bot Setup

1. Create bot with @BotFather
2. Get bot token
3. Get your admin chat ID
4. Update `.env` file

## 📋 Services Overview

### 🔧 VPN API (Port 3000)
- Main backend service
- Database management
- Business logic
- Auto provisioning
- REST API endpoints

**Key endpoints:**
- `GET /health` - Health check
- `GET /api/plans` - Get VPN plans
- `POST /api/orders` - Create order
- `GET /api/orders` - Get orders
- `POST /api/orders/:id/confirm` - Confirm payment

### 🤖 Telegram Poller
- Telegram bot service
- User interaction
- Order management
- Payment proof upload
- Notifications

### 🎛️ Frontend Admin (Port 3002)
- React-based admin panel
- Order management
- CHR device management
- User management
- Dashboard & analytics

### 🌐 Mikrotik API (Port 3003)
- Mikrotik CHR communication
- PPP secret management
- Port forwarding setup
- Connection testing

**Key endpoints:**
- `POST /api/mikrotik/test-connection` - Test CHR connection
- `POST /api/mikrotik/create-account` - Create VPN account
- `POST /api/mikrotik/add-port-forward` - Add port forwarding

### 🗄️ PostgreSQL (Port 5433)
- Main database
- User data
- Orders & payments
- VPN accounts
- CHR devices

## 🔄 Workflow

### User Order Flow
1. User starts bot with `/start`
2. User selects VPN plan via `/menu`
3. User uploads payment proof
4. Admin gets notification
5. Admin confirms payment in panel
6. System auto-provisions VPN account
7. User receives VPN credentials

### Admin Management Flow
1. Admin accesses panel at http://localhost:3002
2. Views pending orders
3. Confirms payments
4. Manages CHR devices
5. Monitors active accounts
6. Extends accounts if needed

## 🛠️ Development

### Project Structure
```
vpn-manager/
├── docker-compose.yml
├── init.sql
├── .env
├── telegram-poller/          # Telegram bot service
├── vpn-api/                  # Main backend API
├── frontend-admin/           # React admin panel
├── mikrotik-api/            # Mikrotik communication
└── uploads/                 # Payment proof uploads
```

### Adding CHR Device
1. Access admin panel
2. Go to "CHR Devices"
3. Click "Add CHR Device"
4. Fill device details
5. Test connection
6. Save device

### Adding VPN Plans
1. Access admin panel
2. Go to "VPN Plans"
3. Click "Add VPN Plan"
4. Configure plan details
5. Set pricing and bandwidth
6. Activate plan

## 🔍 Monitoring & Logs

### View logs
```bash
# All services
docker compose logs

# Specific service
docker compose logs vpn-api
docker compose logs telegram-poller
docker compose logs frontend-admin
docker compose logs mikrotik-api
```

### Database access
```bash
# Connect to database
docker compose exec postgres psql -U vpn_user -d vpn_service

# View tables
\dt

# Check orders
SELECT * FROM orders;

# Check VPN accounts
SELECT * FROM vpn_accounts;
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   docker compose down
   docker compose up -d
   ```

2. **Database connection issues**
   - Check PostgreSQL is healthy: `docker compose ps`
   - Verify environment variables in `.env`

3. **Telegram bot not responding**
   - Check bot token in `.env`
   - Verify bot is started with @BotFather

4. **CHR connection failed**
   - Verify CHR device credentials
   - Check network connectivity
   - Ensure API port (8728) is accessible

### Reset Everything
```bash
docker compose down -v
docker compose up -d
```

## 📚 API Documentation

### VPN Plans
```bash
# Get all plans
curl http://localhost:3000/api/plans

# Get plans by type
curl http://localhost:3000/api/plans/type/internet
```

### Orders
```bash
# Create order
curl -X POST http://localhost:3000/api/orders \
  -H "Content-Type: application/json" \
  -d '{"user_id": 1, "plan_id": 1}'

# Get orders
curl http://localhost:3000/api/orders
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check troubleshooting section
- Review logs for errors
- Create issue in repository

---

**Built with ❤️ for VPN service providers**
