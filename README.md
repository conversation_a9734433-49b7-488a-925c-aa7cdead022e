# VPN Service Platform

Platform penjualan layanan VPN berbasis Mikrotik CHR dengan bot Telegram dan panel admin.

## Fitur
- Penjualan VPN Internet, Game, dan Remote
- Bot Telegram untuk pemesanan
- Panel admin untuk manajemen
- Auto provisioning akun VPN
- Manajemen multi-CHR

## Quick Start
```bash
docker compose up -d
```

## Services
- **telegram-poller**: Bot Telegram (Port 3001)
- **vpn-api**: Backend API (Port 3000)
- **frontend-admin**: Admin Panel (Port 3002)
- **mikrotik-api**: Mikrotik API (Port 3003)
- **postgres**: Database (Port 5432)

## Environment Variables
Copy `.env.example` to `.env` and configure:
- TELEGRAM_BOT_TOKEN
- TELEGRAM_ADMIN_CHAT_ID
- Database credentials

## Documentation
See `DEVELOPMENT.md` for detailed specifications.
